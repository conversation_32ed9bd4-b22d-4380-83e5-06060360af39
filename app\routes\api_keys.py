"""API Key management endpoints."""
from __future__ import annotations

from http import HTTPStatus

from flask import Blueprint, jsonify
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import <PERSON><PERSON>ey, User
from ..schemas import APIKeySchema

api_key_bp = Blueprint("api_keys", __name__)

api_key_schema = APIKeySchema()
api_keys_schema = APIKeySchema(many=True)


@api_key_bp.post("/")
@jwt_required()
def create_api_key():
    """Create a new API key for the current user."""
    user_id: int = get_jwt_identity()  # type: ignore
    current_user: User = User.query.get_or_404(user_id)

    key, prefix = APIKey.generate_key()
    key_hash = APIKey.hash_key(key)

    api_key = APIKey(
        user=current_user,
        key_hash=key_hash,
        prefix=prefix,
    )
    db.session.add(api_key)
    db.session.commit()

    # Return the full key to the user *only* on creation
    response_data = api_key_schema.dump(api_key)
    response_data["key"] = key

    return jsonify(response_data), HTTPStatus.CREATED


@api_key_bp.get("/")
@jwt_required()
def list_api_keys():
    """List active API keys for the current user."""
    user_id: int = get_jwt_identity()  # type: ignore
    keys = APIKey.query.filter_by(user_id=user_id, is_active=True).all()
    return api_keys_schema.jsonify(keys)


@api_key_bp.delete("/<int:key_id>")
@jwt_required()
def revoke_api_key(key_id: int):
    """Revoke (deactivate) an API key."""
    user_id: int = get_jwt_identity()  # type: ignore
    api_key: APIKey = APIKey.query.filter_by(
        id=key_id, user_id=user_id
    ).first_or_404()

    api_key.is_active = False
    db.session.commit()

    return "", HTTPStatus.NO_CONTENT
