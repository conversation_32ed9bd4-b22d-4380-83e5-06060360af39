"""Secondary password management with lockout mechanism."""
from __future__ import annotations

from http import HTT<PERSON>tatus
from datetime import datetime, timedelta
import ipaddress

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import bcrypt, db
from ..models import User, SecondaryPasswordAttempt
from ..routes.admin_helpers import audit_log
from ..tasks import send_email

secondary_password_bp = Blueprint("secondary_password", __name__)


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


def get_client_ip() -> str:
    """Get the client's IP address."""
    # Check for forwarded IP first (for reverse proxies)
    if request.headers.get("X-Forwarded-For"):
        return request.headers.get("X-Forwarded-For").split(",")[0].strip()
    elif request.headers.get("X-Real-IP"):
        return request.headers.get("X-Real-IP")
    else:
        return request.remote_addr or "unknown"


def calculate_lockout_duration(failed_attempts: int) -> timedelta:
    """Calculate lockout duration based on number of failed attempts."""
    lockout_schedule = {
        5: timedelta(hours=24),      # 24 hours
        6: timedelta(hours=48),      # 48 hours  
        7: timedelta(hours=72),      # 72 hours
        8: timedelta(days=7),        # 1 week
        9: timedelta(days=30),       # 1 month
        10: timedelta(days=365),     # 1 year
    }
    
    if failed_attempts >= 11:
        return timedelta(days=365 * 100)  # Permanent lockout (100 years)
    
    return lockout_schedule.get(failed_attempts, timedelta(0))


def is_user_locked_out(user: User) -> tuple[bool, str | None]:
    """Check if user is currently locked out from secondary password attempts."""
    # Get recent failed attempts (last 24 hours for counting)
    recent_cutoff = datetime.utcnow() - timedelta(hours=24)
    
    failed_attempts = SecondaryPasswordAttempt.query.filter(
        SecondaryPasswordAttempt.user_id == user.id,
        SecondaryPasswordAttempt.success == False,
        SecondaryPasswordAttempt.created_at >= recent_cutoff
    ).count()
    
    if failed_attempts < 5:
        return False, None
    
    # Get the most recent failed attempt to calculate lockout end time
    last_failed_attempt = SecondaryPasswordAttempt.query.filter(
        SecondaryPasswordAttempt.user_id == user.id,
        SecondaryPasswordAttempt.success == False
    ).order_by(SecondaryPasswordAttempt.created_at.desc()).first()
    
    if not last_failed_attempt:
        return False, None
    
    lockout_duration = calculate_lockout_duration(failed_attempts)
    lockout_end = last_failed_attempt.created_at + lockout_duration
    
    if datetime.utcnow() < lockout_end:
        if failed_attempts >= 11:
            return True, "Account permanently locked due to too many failed attempts"
        else:
            return True, f"Account locked until {lockout_end.isoformat()}Z"
    
    return False, None


def record_password_attempt(user: User, success: bool) -> None:
    """Record a secondary password attempt and send notifications on lockout thresholds."""
    attempt = SecondaryPasswordAttempt(
        user_id=user.id,
        ip_address=get_client_ip(),
        user_agent=request.headers.get("User-Agent"),
        success=success
    )
    db.session.add(attempt)
    db.session.commit()
    
    # Send email notifications on lockout thresholds
    if not success:
        # Count recent failed attempts
        recent_cutoff = datetime.utcnow() - timedelta(hours=24)
        failed_attempts = SecondaryPasswordAttempt.query.filter(
            SecondaryPasswordAttempt.user_id == user.id,
            SecondaryPasswordAttempt.success == False,
            SecondaryPasswordAttempt.created_at >= recent_cutoff
        ).count()
        
        # Send notification emails at specific thresholds
        if failed_attempts in [5, 6, 7, 8, 9, 10, 11]:
            lockout_duration = calculate_lockout_duration(failed_attempts)
            
            if failed_attempts >= 11:
                subject = "🔒 Account Permanently Locked - Cloud OTP"
                body = f"""
Your Cloud OTP account has been permanently locked due to 11 failed secondary password attempts.

This is a security measure to protect your encrypted vault. Your data remains secure and encrypted, but is now permanently inaccessible.

Account: {user.email}
Time: {datetime.utcnow().isoformat()}Z
IP Address: {get_client_ip()}

No recovery is possible. This action cannot be undone.

If you believe this was unauthorized access, please contact our security team immediately.

Cloud OTP Security Team
"""
            else:
                lockout_end = datetime.utcnow() + lockout_duration
                subject = f"🔒 Account Locked for {lockout_duration} - Cloud OTP"
                body = f"""
Your Cloud OTP account has been temporarily locked due to {failed_attempts} failed secondary password attempts.

Account: {user.email}
Locked until: {lockout_end.isoformat()}Z
Time: {datetime.utcnow().isoformat()}Z
IP Address: {get_client_ip()}

This is a security measure to protect your encrypted vault. After the lockout period expires, you can try again.

Remaining attempts before permanent lockout: {11 - failed_attempts}

If you believe this was unauthorized access, please contact our security team immediately.

Cloud OTP Security Team
"""
            
            # Send email notification asynchronously
            send_email.delay(
                to=user.email,
                subject=subject,
                body=body
            )


@secondary_password_bp.route("/set", methods=["POST"])
@jwt_required()
@audit_log("Set secondary password")
def set_secondary_password():
    """Set or update the secondary password and encrypted DEK for the user."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    secondary_password = data.get("secondary_password")
    encrypted_dek = data.get("encrypted_dek")
    current_secondary_password = data.get("current_secondary_password")
    
    if not secondary_password or not encrypted_dek:
        return jsonify({
            "msg": "secondary_password and encrypted_dek are required"
        }), HTTPStatus.BAD_REQUEST
    
    # If user already has a secondary password, they must provide the current one
    if current_user.secondary_password_hash and not current_secondary_password:
        return jsonify({
            "msg": "current_secondary_password is required to update"
        }), HTTPStatus.BAD_REQUEST
    
    # Verify current secondary password if provided
    if current_user.secondary_password_hash and current_secondary_password:
        if not bcrypt.check_password_hash(current_user.secondary_password_hash, current_secondary_password):
            record_password_attempt(current_user, False)
            return jsonify({"msg": "Invalid current secondary password"}), HTTPStatus.UNAUTHORIZED
    
    # Validate password strength (basic validation - should be enhanced)
    if len(secondary_password) < 16:
        return jsonify({
            "msg": "Secondary password must be at least 16 characters long"
        }), HTTPStatus.BAD_REQUEST
    
    # Update secondary password and DEK
    current_user.secondary_password_hash = bcrypt.generate_password_hash(secondary_password).decode()
    current_user.encrypted_dek = encrypted_dek
    
    db.session.commit()
    
    return jsonify({"msg": "Secondary password updated successfully"}), HTTPStatus.OK


@secondary_password_bp.route("/verify", methods=["POST"])
@jwt_required()
@audit_log("Verified secondary password")
def verify_secondary_password():
    """Verify the secondary password and return the encrypted DEK."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    secondary_password = data.get("secondary_password")
    
    if not secondary_password:
        return jsonify({"msg": "secondary_password is required"}), HTTPStatus.BAD_REQUEST
    
    if not current_user.secondary_password_hash:
        return jsonify({"msg": "No secondary password set"}), HTTPStatus.NOT_FOUND
    
    # Check if user is locked out
    is_locked, lockout_message = is_user_locked_out(current_user)
    if is_locked:
        return jsonify({"msg": lockout_message}), HTTPStatus.LOCKED
    
    # Verify password
    if bcrypt.check_password_hash(current_user.secondary_password_hash, secondary_password):
        record_password_attempt(current_user, True)
        return jsonify({
            "msg": "Secondary password verified",
            "encrypted_dek": current_user.encrypted_dek
        }), HTTPStatus.OK
    else:
        record_password_attempt(current_user, False)
        
        # Check if this attempt triggers a lockout
        recent_cutoff = datetime.utcnow() - timedelta(hours=24)
        failed_attempts = SecondaryPasswordAttempt.query.filter(
            SecondaryPasswordAttempt.user_id == current_user.id,
            SecondaryPasswordAttempt.success == False,
            SecondaryPasswordAttempt.created_at >= recent_cutoff
        ).count()
        
        if failed_attempts >= 5:
            lockout_duration = calculate_lockout_duration(failed_attempts)
            if failed_attempts >= 11:
                return jsonify({
                    "msg": "Account permanently locked due to too many failed attempts"
                }), HTTPStatus.LOCKED
            else:
                lockout_end = datetime.utcnow() + lockout_duration
                return jsonify({
                    "msg": f"Account locked until {lockout_end.isoformat()}Z due to failed attempts"
                }), HTTPStatus.LOCKED
        
        remaining_attempts = 5 - failed_attempts
        if remaining_attempts > 0:
            return jsonify({
                "msg": f"Invalid secondary password. {remaining_attempts} attempts remaining before lockout"
            }), HTTPStatus.UNAUTHORIZED
        else:
            return jsonify({"msg": "Invalid secondary password"}), HTTPStatus.UNAUTHORIZED


@secondary_password_bp.route("/status", methods=["GET"])
@jwt_required()
def get_secondary_password_status():
    """Get the status of the user's secondary password setup."""
    current_user = get_current_user()
    
    has_secondary_password = bool(current_user.secondary_password_hash)
    has_encrypted_dek = bool(current_user.encrypted_dek)
    
    # Check lockout status
    is_locked, lockout_message = is_user_locked_out(current_user)
    
    # Get recent attempt count
    recent_cutoff = datetime.utcnow() - timedelta(hours=24)
    recent_failed_attempts = SecondaryPasswordAttempt.query.filter(
        SecondaryPasswordAttempt.user_id == current_user.id,
        SecondaryPasswordAttempt.success == False,
        SecondaryPasswordAttempt.created_at >= recent_cutoff
    ).count()
    
    return jsonify({
        "has_secondary_password": has_secondary_password,
        "has_encrypted_dek": has_encrypted_dek,
        "is_locked_out": is_locked,
        "lockout_message": lockout_message,
        "recent_failed_attempts": recent_failed_attempts,
        "setup_complete": has_secondary_password and has_encrypted_dek
    })


@secondary_password_bp.route("/attempts", methods=["GET"])
@jwt_required()
def get_password_attempts():
    """Get recent secondary password attempts for the user."""
    current_user = get_current_user()
    
    # Get last 50 attempts
    attempts = SecondaryPasswordAttempt.query.filter_by(
        user_id=current_user.id
    ).order_by(SecondaryPasswordAttempt.created_at.desc()).limit(50).all()
    
    attempt_data = []
    for attempt in attempts:
        attempt_data.append({
            "timestamp": attempt.created_at.isoformat() + "Z",
            "ip_address": attempt.ip_address,
            "success": attempt.success,
            "user_agent": attempt.user_agent
        })
    
    return jsonify({"attempts": attempt_data})


@secondary_password_bp.route("/reset-lockout", methods=["POST"])
@jwt_required()
def reset_lockout():
    """Reset lockout status (admin only or with proper verification)."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    # This is a dangerous operation - require admin privileges or strong verification
    if not current_user.has_role("Super Admin"):
        return jsonify({"msg": "Admin privileges required"}), HTTPStatus.FORBIDDEN
    
    target_user_id = data.get("user_id")
    if not target_user_id:
        return jsonify({"msg": "user_id is required"}), HTTPStatus.BAD_REQUEST
    
    target_user = User.query.get_or_404(target_user_id)
    
    # Delete all failed attempts to reset lockout
    SecondaryPasswordAttempt.query.filter_by(
        user_id=target_user.id,
        success=False
    ).delete()
    
    db.session.commit()
    
    return jsonify({"msg": f"Lockout reset for user {target_user.email}"}), HTTPStatus.OK


# Admin endpoints for monitoring
@secondary_password_bp.route("/admin/locked-users", methods=["GET"])
@jwt_required()
def get_locked_users():
    """Get list of users currently locked out (admin only)."""
    current_user = get_current_user()
    
    if not current_user.has_role("Super Admin"):
        return jsonify({"msg": "Admin privileges required"}), HTTPStatus.FORBIDDEN
    
    # Find users with recent failed attempts
    recent_cutoff = datetime.utcnow() - timedelta(hours=24)
    
    locked_users = []
    users_with_attempts = db.session.query(
        SecondaryPasswordAttempt.user_id,
        db.func.count(SecondaryPasswordAttempt.id).label("failed_count")
    ).filter(
        SecondaryPasswordAttempt.success == False,
        SecondaryPasswordAttempt.created_at >= recent_cutoff
    ).group_by(SecondaryPasswordAttempt.user_id).having(
        db.func.count(SecondaryPasswordAttempt.id) >= 5
    ).all()
    
    for user_id, failed_count in users_with_attempts:
        user = User.query.get(user_id)
        if user:
            is_locked, lockout_message = is_user_locked_out(user)
            if is_locked:
                locked_users.append({
                    "user_id": user.id,
                    "email": user.email,
                    "failed_attempts": failed_count,
                    "lockout_message": lockout_message
                })
    
    return jsonify({"locked_users": locked_users})


@secondary_password_bp.route("/admin/stats", methods=["GET"])
@jwt_required()
def get_secondary_password_stats():
    """Get secondary password statistics (admin only)."""
    current_user = get_current_user()
    
    if not current_user.has_role("Super Admin"):
        return jsonify({"msg": "Admin privileges required"}), HTTPStatus.FORBIDDEN
    
    total_users = User.query.count()
    users_with_secondary_password = User.query.filter(
        User.secondary_password_hash.isnot(None)
    ).count()
    
    # Recent attempts (last 24 hours)
    recent_cutoff = datetime.utcnow() - timedelta(hours=24)
    recent_attempts = SecondaryPasswordAttempt.query.filter(
        SecondaryPasswordAttempt.created_at >= recent_cutoff
    ).count()
    
    recent_failed_attempts = SecondaryPasswordAttempt.query.filter(
        SecondaryPasswordAttempt.created_at >= recent_cutoff,
        SecondaryPasswordAttempt.success == False
    ).count()
    
    return jsonify({
        "total_users": total_users,
        "users_with_secondary_password": users_with_secondary_password,
        "setup_percentage": round((users_with_secondary_password / total_users) * 100, 2) if total_users > 0 else 0,
        "recent_attempts": recent_attempts,
        "recent_failed_attempts": recent_failed_attempts,
        "recent_success_rate": round(((recent_attempts - recent_failed_attempts) / recent_attempts) * 100, 2) if recent_attempts > 0 else 0
    })