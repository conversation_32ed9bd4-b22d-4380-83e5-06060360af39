"""SQLAlchemy models for Cloud OTP core entities."""
from __future__ import annotations

from datetime import datetime
from typing import Any
import secrets
import hashlib

from sqlalchemy import (
    Column,
    Table,
    UniqueConstraint,
    func,
    Foreign<PERSON>ey,
    String,
    Integer,
    Boolean,
    DateTime,
    Text,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .extensions import db

# Helper type for JSON column on SQLite
JSON: Any = db.JSON if hasattr(db, "JSON") else db.PickleType  # type: ignore


class TimestampMixin:
    """Mixin adding created/updated timestamps."""

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )


# Association table for many-to-many relationship between User and Role
user_roles = Table(
    "user_roles",
    db.metadata,
    Column("user_id", ForeignKey("users.id"), primary_key=True),
    <PERSON>umn("role_id", ForeignKey("roles.id"), primary_key=True),
)


class Role(db.Model):  # type: ignore[misc]
    """User roles (e.g., Super Admin, Developer Admin)."""

    __tablename__ = "roles"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(80), unique=True, nullable=False)
    description: Mapped[str | None] = mapped_column(String(255))

    def __repr__(self) -> str:
        return f"<Role {self.name}>"


class User(TimestampMixin, db.Model):  # type: ignore[misc]
    """User accounts (individual or team owners)."""

    __tablename__ = "users"

    id: Mapped[int] = mapped_column(primary_key=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    password_hash: Mapped[str] = mapped_column(String(128), nullable=False)
    first_name: Mapped[str | None] = mapped_column(String(100))
    last_name: Mapped[str | None] = mapped_column(String(100))
    phone_number: Mapped[str | None] = mapped_column(String(50), unique=True)

    # Zero-Knowledge Encryption fields
    secondary_password_hash: Mapped[str | None] = mapped_column(String(128))
    encrypted_dek: Mapped[str | None] = mapped_column(Text)  # Encrypted Data Encryption Key

    is_active: Mapped[bool] = mapped_column(default=True)
    credits: Mapped[int] = mapped_column(Integer, default=0)

    # Resource allocation
    api_quota_per_month: Mapped[int | None] = mapped_column(Integer)
    api_rate_limit_per_second: Mapped[int | None] = mapped_column(Integer)
    storage_limit: Mapped[int | None] = mapped_column(Integer, nullable=True)

    # Relationships
    roles: Mapped[list["Role"]] = relationship(secondary=user_roles, backref="users")
    otps: Mapped[list["OTP"]] = relationship(
        back_populates="owner", cascade="all,delete-orphan"
    )
    api_keys: Mapped[list["APIKey"]] = relationship(
        back_populates="user", cascade="all,delete-orphan"
    )
    webhook_configs: Mapped[list["WebhookConfig"]] = relationship(
        back_populates="user", cascade="all,delete-orphan"
    )
    teams: Mapped[list["TeamMember"]] = relationship(
        back_populates="user", cascade="all,delete-orphan"
    )
    referred_by_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"), nullable=True)
    campaign_id: Mapped[int | None] = mapped_column(ForeignKey("campaigns.id"), nullable=True)
    referred_by: Mapped[list["User"]] = relationship("User", remote_side=[id], backref="referrals_received")
    campaign: Mapped[list["Campaign"]] = relationship("Campaign", back_populates="users")

    def has_role(self, role_name: str) -> bool:
        """Check if a user has a specific role, supporting aliases."""
        role_aliases = {
            "Sales": ["Sales", "Sales Admin"],
            "Sales Admin": ["Sales", "Sales Admin"],
            "Marketing": ["Marketing", "Marketing Admin"],
            "Marketing Admin": ["Marketing", "Marketing Admin"],
            "Developer Admin": ["Developer Admin"],
            "Support": ["Support"],
            "Super Admin": ["Super Admin"],
        }
        aliases = role_aliases.get(role_name, [role_name])
        return any(role.name in aliases for role in self.roles)


class Team(TimestampMixin, db.Model):  # type: ignore[misc]
    """Team accounts."""

    __tablename__ = "teams"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    credits: Mapped[int] = mapped_column(Integer, default=0)

    # Resource allocation
    api_quota_per_month: Mapped[int | None] = mapped_column(Integer)
    api_rate_limit_per_second: Mapped[int | None] = mapped_column(Integer)
    storage_limit: Mapped[int | None] = mapped_column(Integer, nullable=True)

    # Relationships
    owner: Mapped["User"] = relationship(backref="owned_teams")
    members: Mapped[list["TeamMember"]] = relationship(
        back_populates="team", cascade="all,delete-orphan"
    )
    otps: Mapped[list["OTP"]] = relationship(
        back_populates="team", cascade="all,delete-orphan"
    )
    api_keys: Mapped[list["APIKey"]] = relationship(
        back_populates="team", cascade="all,delete-orphan"
    )
    webhook_configs: Mapped[list["WebhookConfig"]] = relationship(
        back_populates="team", cascade="all,delete-orphan"
    )


class TeamMember(TimestampMixin, db.Model):  # type: ignore[misc]
    """Association object for User and Team membership."""

    __tablename__ = "team_members"
    __table_args__ = (UniqueConstraint("user_id", "team_id"),)

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    team_id: Mapped[int] = mapped_column(ForeignKey("teams.id"), nullable=False)
    # e.g., 'admin', 'member', 'read-only'
    role: Mapped[str] = mapped_column(String(50), nullable=False, default="member")

    # Relationships
    user: Mapped["User"] = relationship(back_populates="teams")
    team: Mapped["Team"] = relationship(back_populates="members")


class APIKey(TimestampMixin, db.Model):  # type: ignore[misc]
    """REST API keys for users or teams."""

    __tablename__ = "api_keys"

    id: Mapped[int] = mapped_column(primary_key=True)
    key_hash: Mapped[str] = mapped_column(String(128), unique=True, nullable=False)
    prefix: Mapped[str] = mapped_column(String(8), unique=True, nullable=False)
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    last_used_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    is_active: Mapped[bool] = mapped_column(default=True)

    # Relationships
    user: Mapped["User" | None] = relationship(back_populates="api_keys")
    team: Mapped["Team" | None] = relationship(back_populates="api_keys")

    @staticmethod
    def generate_key() -> tuple[str, str]:
        """Generate a new API key and prefix."""
        prefix = f"cotp_{secrets.token_urlsafe(4)}"
        key = secrets.token_urlsafe(32)
        return f"{prefix}_{key}", prefix

    @staticmethod
    def hash_key(key: str) -> str:
        """Hash the API key for storage."""
        return hashlib.sha256(key.encode()).hexdigest()


class OTP(TimestampMixin, db.Model):  # type: ignore[misc]
    """One-time password secret entry."""

    __tablename__ = "otps"
    __table_args__ = (
        UniqueConstraint("owner_id", "name", name="uq_owner_name"),
        UniqueConstraint("team_id", "name", name="uq_team_name"),
    )

    id: Mapped[int] = mapped_column(primary_key=True)
    owner_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    folder_id: Mapped[int | None] = mapped_column(ForeignKey("folders.id"))

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    # Secret is encrypted client-side using the user's DEK before being sent to the server.
    secret_encrypted: Mapped[str] = mapped_column(Text, nullable=False)
    algorithm: Mapped[str] = mapped_column(String(20), default="SHA1")
    digits: Mapped[int] = mapped_column(Integer, default=6)
    period: Mapped[int] = mapped_column(Integer, default=30)
    counter: Mapped[int | None] = mapped_column(Integer)

    metadata: Mapped[JSON] = mapped_column(JSON, default=dict)

    # Relationships
    owner: Mapped["User" | None] = relationship(back_populates="otps")
    team: Mapped["Team" | None] = relationship(back_populates="otps")
    folder: Mapped["Folder" | None] = relationship(back_populates="otps")


class Coupon(TimestampMixin, db.Model):  # type: ignore[misc]
    """Coupon codes for discounts or credit."""

    __tablename__ = "coupons"

    id: Mapped[int] = mapped_column(primary_key=True)
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    # e.g., 'percentage', 'fixed_amount', 'credits'
    discount_type: Mapped[str] = mapped_column(String(50), nullable=False)
    value: Mapped[int] = mapped_column(Integer, nullable=False)  # In cents or credits
    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    max_uses: Mapped[int | None] = mapped_column(Integer)
    use_count: Mapped[int] = mapped_column(Integer, default=0)
    is_active: Mapped[bool] = mapped_column(default=True)
    campaign_id: Mapped[int | None] = mapped_column(ForeignKey("campaigns.id"), nullable=True)
    campaign: Mapped[list["Campaign"]] = relationship("Campaign", back_populates="coupons")
    referrals: Mapped[list["Referral"]] = relationship("Referral", back_populates="coupon")


class Invitation(TimestampMixin, db.Model):  # type: ignore[misc]
    """Invitation codes for new user or team sign-ups and team member invitations."""

    __tablename__ = "invitations"

    id: Mapped[int] = mapped_column(primary_key=True)
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    created_by_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    email: Mapped[str | None] = mapped_column(String(255))  # For team invitations
    token: Mapped[str | None] = mapped_column(String(128), unique=True)  # Secure invitation token
    status: Mapped[str] = mapped_column(String(32), default="pending")  # pending, accepted, revoked, expired
    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    is_active: Mapped[bool] = mapped_column(default=True)
    campaign_id: Mapped[int | None] = mapped_column(ForeignKey("campaigns.id"), nullable=True)

    # Relationships
    created_by: Mapped["User"] = relationship(backref="created_invitations")
    team: Mapped["Team" | None] = relationship(backref="invitations")
    campaign: Mapped[list["Campaign"]] = relationship("Campaign", back_populates="invitations")
    referrals: Mapped[list["Referral"]] = relationship("Referral", back_populates="invitation")


class AuditLog(TimestampMixin, db.Model):  # type: ignore[misc]
    """Logs for critical actions in the system."""

    __tablename__ = "audit_logs"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    action: Mapped[str] = mapped_column(String(255), nullable=False)
    details: Mapped[JSON] = mapped_column(JSON, default=dict)

    # Relationships
    user: Mapped["User" | None] = relationship(backref="audit_logs")
    team: Mapped["Team" | None] = relationship(backref="audit_logs")


class SystemSetting(db.Model):  # type: ignore[misc]
    """Key-value store for system-wide settings."""

    __tablename__ = "system_settings"

    key: Mapped[str] = mapped_column(String(50), primary_key=True)
    value: Mapped[Any] = mapped_column(JSON, nullable=False)


class Ticket(TimestampMixin, db.Model):  # type: ignore[misc]
    """Support tickets."""

    __tablename__ = "tickets"

    id: Mapped[int] = mapped_column(primary_key=True)
    subject: Mapped[str] = mapped_column(String(255), nullable=False)
    status: Mapped[str] = mapped_column(String(50), default="open")  # open, in_progress, closed
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)

    # Relationships
    user: Mapped["User"] = relationship(backref="tickets")
    messages: Mapped[list["TicketMessage"]] = relationship(
        back_populates="ticket", cascade="all,delete-orphan"
    )


class TicketMessage(TimestampMixin, db.Model):  # type: ignore[misc]
    """Messages within a support ticket."""

    __tablename__ = "ticket_messages"

    id: Mapped[int] = mapped_column(primary_key=True)
    ticket_id: Mapped[int] = mapped_column(ForeignKey("tickets.id"), nullable=False)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)

    # Relationships
    ticket: Mapped["Ticket"] = relationship(back_populates="messages")
    user: Mapped["User"] = relationship(backref="ticket_messages")


class Payment(TimestampMixin, db.Model):  # type: ignore[misc]
    """Payment events from Paddle."""

    __tablename__ = "payments"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    paddle_event_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    event_type: Mapped[str] = mapped_column(String(100), nullable=False)
    amount: Mapped[int] = mapped_column(Integer)  # In cents
    currency: Mapped[str] = mapped_column(String(10))
    payload: Mapped[JSON] = mapped_column(JSON, nullable=False)

    # Relationships
    user: Mapped["User" | None] = relationship(backref="payments")
    team: Mapped["Team" | None] = relationship(backref="payments")


class Folder(TimestampMixin, db.Model):  # type: ignore[misc]
    """Folders for organizing OTPs."""

    __tablename__ = "folders"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str | None] = mapped_column(Text)
    owner_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    parent_id: Mapped[int | None] = mapped_column(ForeignKey("folders.id"))

    # Relationships
    owner: Mapped["User" | None] = relationship(backref="folders")
    team: Mapped["Team" | None] = relationship(backref="folders")
    parent: Mapped["Folder" | None] = relationship("Folder", remote_side=[id], backref="children")
    otps: Mapped[list["OTP"]] = relationship(back_populates="folder")


class OTPShare(TimestampMixin, db.Model):  # type: ignore[misc]
    """OTP sharing records."""

    __tablename__ = "otp_shares"

    id: Mapped[int] = mapped_column(primary_key=True)
    otp_id: Mapped[int] = mapped_column(ForeignKey("otps.id"), nullable=False)
    shared_by_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    shared_with_user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    shared_with_team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))
    
    # Share configuration
    share_type: Mapped[str] = mapped_column(String(20), default="user")  # "user", "team", "public"
    permission: Mapped[str] = mapped_column(String(20), default="read")  # "read", "write", "admin"
    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    password_hash: Mapped[str | None] = mapped_column(String(128))  # For password-protected public shares
    access_limit: Mapped[int | None] = mapped_column(Integer)  # Max number of accesses
    access_count: Mapped[int] = mapped_column(Integer, default=0)
    is_active: Mapped[bool] = mapped_column(default=True)
    
    # Public share token for URL access
    share_token: Mapped[str | None] = mapped_column(String(64), unique=True)

    # Relationships
    otp: Mapped["OTP"] = relationship(backref="shares")
    shared_by: Mapped["User"] = relationship("User", foreign_keys=[shared_by_user_id], backref="shared_otps")
    shared_with_user: Mapped["User" | None] = relationship("User", foreign_keys=[shared_with_user_id])
    shared_with_team: Mapped["Team" | None] = relationship("Team", backref="shared_otps")


class Note(TimestampMixin, db.Model):  # type: ignore[misc]
    """Notes attached to OTP entries."""

    __tablename__ = "notes"

    id: Mapped[int] = mapped_column(primary_key=True)
    otp_id: Mapped[int] = mapped_column(ForeignKey("otps.id"), nullable=False)
    author_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    title: Mapped[str] = mapped_column(String(255), default="")
    content_encrypted: Mapped[str] = mapped_column(Text, nullable=False)  # Encrypted with user's DEK

    # Relationships
    otp: Mapped["OTP"] = relationship(backref="notes")
    author: Mapped["User"] = relationship(backref="authored_notes")


class SecondaryPasswordAttempt(TimestampMixin, db.Model):  # type: ignore[misc]
    """Track secondary password attempts for lockout mechanism."""

    __tablename__ = "secondary_password_attempts"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    ip_address: Mapped[str] = mapped_column(String(45))  # IPv6 compatible
    user_agent: Mapped[str | None] = mapped_column(Text)
    success: Mapped[bool] = mapped_column(default=False)
    
    # Relationships
    user: Mapped["User"] = relationship(backref="password_attempts")


class WebhookEvent(TimestampMixin, db.Model):  # type: ignore[misc]
    """Webhook events for external integrations."""

    __tablename__ = "webhook_events"

    id: Mapped[int] = mapped_column(primary_key=True)
    event_type: Mapped[str] = mapped_column(String(100), nullable=False)
    payload: Mapped[JSON] = mapped_column(JSON, nullable=False)
    target_url: Mapped[str] = mapped_column(String(500), nullable=False)
    status: Mapped[str] = mapped_column(String(20), default="pending")  # pending, sent, failed
    attempts: Mapped[int] = mapped_column(Integer, default=0)
    last_attempt_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    response_status: Mapped[int | None] = mapped_column(Integer)
    response_body: Mapped[str | None] = mapped_column(Text)


class TeamResourceAllocation(TimestampMixin, db.Model):  # type: ignore[misc]
    """Resource allocation and usage for teams (e.g., max users, API keys, OTPs)."""

    __tablename__ = "team_resource_allocations"
    id: Mapped[int] = mapped_column(primary_key=True)
    team_id: Mapped[int] = mapped_column(ForeignKey("teams.id"), nullable=False)
    resource_type: Mapped[str] = mapped_column(String(50), nullable=False)  # e.g., 'users', 'api_keys', 'otps'
    limit: Mapped[int] = mapped_column(Integer, nullable=False)
    used: Mapped[int] = mapped_column(Integer, default=0)
    # Relationships
    team: Mapped["Team"] = relationship(backref="resource_allocations")


class FolderShare(TimestampMixin, db.Model):  # type: ignore[misc]
    """Folder sharing records (mirrors OTPShare for folders)."""

    __tablename__ = "folder_shares"

    id: Mapped[int] = mapped_column(primary_key=True)
    folder_id: Mapped[int] = mapped_column(ForeignKey("folders.id"), nullable=False)
    shared_by_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    shared_with_user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    shared_with_team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))

    # Share configuration
    share_type: Mapped[str] = mapped_column(String(20), default="user")  # "user", "team", "public"
    permission: Mapped[str] = mapped_column(String(20), default="read")  # "read", "write", "admin"
    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    password_hash: Mapped[str | None] = mapped_column(String(128))  # For password-protected public shares
    access_limit: Mapped[int | None] = mapped_column(Integer)  # Max number of accesses
    access_count: Mapped[int] = mapped_column(Integer, default=0)
    is_active: Mapped[bool] = mapped_column(default=True)

    # Public share token for URL access
    share_token: Mapped[str | None] = mapped_column(String(64), unique=True)

    # Relationships
    folder: Mapped["Folder"] = relationship(backref="shares")
    shared_by: Mapped["User"] = relationship("User", foreign_keys=[shared_by_user_id], backref="shared_folders")
    shared_with_user: Mapped["User" | None] = relationship("User", foreign_keys=[shared_with_user_id])
    shared_with_team: Mapped["Team" | None] = relationship("Team", backref="shared_folders")


class ShareAccessLog(TimestampMixin, db.Model):  # type: ignore[misc]
    """Tracks accesses to OTPShare and FolderShare (analytics/auditing)."""

    __tablename__ = "share_access_logs"

    id: Mapped[int] = mapped_column(primary_key=True)
    share_type: Mapped[str] = mapped_column(String(16), nullable=False)  # "otp" or "folder"
    share_id: Mapped[int] = mapped_column(Integer, nullable=False)  # FK to otp_shares.id or folder_shares.id
    accessed_by_user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    ip_address: Mapped[str] = mapped_column(String(45))  # IPv6 compatible
    user_agent: Mapped[str | None] = mapped_column(Text)
    success: Mapped[bool] = mapped_column(default=True)

    # Relationships
    accessed_by: Mapped["User" | None] = relationship("User", backref="share_access_logs")


class WebhookConfig(TimestampMixin, db.Model):  # type: ignore[misc]
    """Webhook configurations for users or teams."""

    __tablename__ = "webhook_configs"
    __table_args__ = (
        UniqueConstraint("user_id", "target_url", name="uq_user_webhook_url"),
        UniqueConstraint("team_id", "target_url", name="uq_team_webhook_url"),
    )

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"))

    target_url: Mapped[str] = mapped_column(String(500), nullable=False)
    secret: Mapped[str | None] = mapped_column(String(128))  # For signing payloads
    is_active: Mapped[bool] = mapped_column(default=True)
    subscribed_events: Mapped[str] = mapped_column(Text, nullable=False, default="*")

    # Relationships
    user: Mapped["User" | None] = relationship(back_populates="webhook_configs")
    team: Mapped["Team" | None] = relationship(back_populates="webhook_configs")


class SAMLIdentityProvider(TimestampMixin, db.Model):  # type: ignore[misc]
    """SAML Identity Provider configuration for a team (Enterprise SSO)."""

    __tablename__ = "saml_identity_providers"
    __table_args__ = (UniqueConstraint("team_id", name="uq_team_saml_idp"),)

    id: Mapped[int] = mapped_column(primary_key=True)
    team_id: Mapped[int] = mapped_column(ForeignKey("teams.id"), nullable=False)

    # SAML IdP metadata
    idp_name: Mapped[str] = mapped_column(String(255), nullable=False)
    entity_id: Mapped[str] = mapped_column(String(512), nullable=False)
    sso_url: Mapped[str] = mapped_column(String(1024), nullable=False)
    x509_cert: Mapped[str] = mapped_column(Text, nullable=False)
    # Optional: SLO URL, x509 cert for signing, etc.
    slo_url: Mapped[str | None] = mapped_column(String(1024))
    x509_cert_signing: Mapped[str | None] = mapped_column(Text)
    # Attribute mapping (JSON: {"email": "EmailAddress", ...})
    attribute_mapping: Mapped[dict] = mapped_column(JSON, default=dict)
    is_active: Mapped[bool] = mapped_column(default=True)
    allow_unsolicited: Mapped[bool] = mapped_column(default=True)  # Allow IdP-initiated login

    # Relationships
    team: Mapped["Team"] = relationship("Team", backref="saml_idp", uselist=False)


# --- CAMPAIGN AND REFERRAL MODELS ---

class Campaign(TimestampMixin, db.Model):
    """Marketing or sales campaign."""
    __tablename__ = "campaigns"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False)  # 'marketing', 'sales', etc.
    description: Mapped[str | None] = mapped_column(Text)
    start_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    end_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    is_active: Mapped[bool] = mapped_column(default=True)
    metadata: Mapped[dict] = mapped_column(JSON, default=dict)

    # Relationships
    invitations: Mapped[list["Invitation"]] = relationship("Invitation", back_populates="campaign")
    coupons: Mapped[list["Coupon"]] = relationship("Coupon", back_populates="campaign")
    referrals: Mapped[list["Referral"]] = relationship("Referral", back_populates="campaign")
    users: Mapped[list["User"]] = relationship("User", back_populates="campaign")

class Referral(TimestampMixin, db.Model):
    """Tracks user signups/referrals, linking users to campaigns and referrers."""
    __tablename__ = "referrals"
    id: Mapped[int] = mapped_column(primary_key=True)
    referred_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    referrer_user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    campaign_id: Mapped[int | None] = mapped_column(ForeignKey("campaigns.id"))
    invitation_id: Mapped[int | None] = mapped_column(ForeignKey("invitations.id"))
    coupon_id: Mapped[int | None] = mapped_column(ForeignKey("coupons.id"))
    method: Mapped[str | None] = mapped_column(String(50))  # 'invitation', 'coupon', 'link', etc.
    metadata: Mapped[dict] = mapped_column(JSON, default=dict)

    # Relationships
    referred_user: Mapped["User"] = relationship("User", foreign_keys=[referred_user_id], backref="referral_record")
    referrer_user: Mapped["User" | None] = relationship("User", foreign_keys=[referrer_user_id], backref="referrals_made")
    campaign: Mapped["Campaign" | None] = relationship("Campaign", back_populates="referrals")
    invitation: Mapped["Invitation" | None] = relationship("Invitation", back_populates="referrals")
    coupon: Mapped["Coupon" | None] = relationship("Coupon", back_populates="referrals")


class Notification(TimestampMixin, db.Model):  # type: ignore[misc]
    """In-app notifications for users and teams (system alerts, etc.)."""
    __tablename__ = "notifications"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"), nullable=True)
    team_id: Mapped[int | None] = mapped_column(ForeignKey("teams.id"), nullable=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False, default="system_alert")  # e.g., 'system_alert', 'credit_low', etc.
    severity: Mapped[str] = mapped_column(String(20), nullable=False, default="info")  # info, warning, critical
    message: Mapped[str] = mapped_column(Text, nullable=False)
    metadata: Mapped[dict] = mapped_column(JSON, default=dict)
    is_read: Mapped[bool] = mapped_column(default=False)
    is_dismissed: Mapped[bool] = mapped_column(default=False)

    # Relationships
    user: Mapped["User" | None] = relationship("User", backref="notifications")
    team: Mapped["Team" | None] = relationship("Team", backref="notifications")
