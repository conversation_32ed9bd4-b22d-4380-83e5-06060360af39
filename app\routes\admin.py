"""Admin dashboard endpoints for managing users, teams, and system settings."""
from __future__ import annotations

from http import HTT<PERSON>tatus


from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from ..extensions import db
from ..models import User, Team, Coupon, Invitation, AuditLog, SystemSetting, Role, Campaign, Referral, Notification
from ..schemas import (
    UserSchema,
    TeamSchema,
    CouponSchema,
    InvitationSchema,
    AuditLogSchema,
    SystemSettingSchema,
    CampaignSchema,
    ReferralSchema,
    NotificationSchema,
)
from .admin_helpers import admin_required, sales_required, marketing_required, audit_log, developer_required

admin_bp = Blueprint("admin", __name__)

user_schema = UserSchema()
users_schema = UserSchema(many=True)
team_schema = TeamSchema()
teams_schema = TeamSchema(many=True)
coupon_schema = CouponSchema()
coupons_schema = CouponSchema(many=True)
invitation_schema = InvitationSchema()
invitations_schema = InvitationSchema(many=True)
audit_log_schema = AuditLogSchema()
audit_logs_schema = AuditLogSchema(many=True)
system_setting_schema = SystemSettingSchema()
system_settings_schema = SystemSettingSchema(many=True)
campaign_schema = CampaignSchema()
campaigns_schema = CampaignSchema(many=True)
referral_schema = ReferralSchema()
referrals_schema = ReferralSchema(many=True)
notification_schema = NotificationSchema()
notifications_schema = NotificationSchema(many=True)


# -----------------------------------------------------------------------------
# Super Admin Dashboard
# -----------------------------------------------------------------------------


def paginate_query(query, schema, default_per_page=20, max_per_page=100):
    """Enhanced pagination with proper SQLAlchemy pagination support."""
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', default_per_page)), max_per_page)
    
    # Use SQLAlchemy's built-in pagination
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return {
        'total': pagination.total,
        'page': pagination.page,
        'per_page': pagination.per_page,
        'pages': pagination.pages,
        'has_prev': pagination.has_prev,
        'has_next': pagination.has_next,
        'prev_num': pagination.prev_num,
        'next_num': pagination.next_num,
        'items': schema.dump(pagination.items)
    }


@admin_bp.route("/users", methods=["GET"])
@jwt_required()
@admin_required
def list_users():
    query = User.query
    return jsonify(paginate_query(query, users_schema))


@admin_bp.route("/users/<int:user_id>", methods=["GET"])
@jwt_required()
@admin_required
def get_user(user_id: int):
    user = User.query.get_or_404(user_id)
    return user_schema.jsonify(user)


@admin_bp.route("/users", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Created user")
def create_user():
    data = request.get_json() or {}
    user = user_schema.load(data)
    db.session.add(user)
    db.session.commit()
    return user_schema.jsonify(user), HTTPStatus.CREATED


@admin_bp.route("/users/<int:user_id>", methods=["PUT"])
@jwt_required()
@admin_required
@audit_log("Updated user")
def update_user(user_id: int):
    user = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    user.email = data.get("email", user.email)
    user.first_name = data.get("first_name", user.first_name)
    user.last_name = data.get("last_name", user.last_name)
    user.phone_number = data.get("phone_number", user.phone_number)
    user.is_active = data.get("is_active", user.is_active)
    user.credits = data.get("credits", user.credits)
    user.api_quota_per_month = data.get(
        "api_quota_per_month", user.api_quota_per_month
    )
    user.api_rate_limit_per_second = data.get(
        "api_rate_limit_per_second", user.api_rate_limit_per_second
    )
    db.session.commit()
    return user_schema.jsonify(user)


@admin_bp.route("/users/<int:user_id>/roles", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Assigned role to user")
def assign_role_to_user(user_id: int):
    user = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    role_name = data.get("role_name")
    role = Role.query.filter_by(name=role_name).first()
    if not role:
        return jsonify({"msg": f"Role '{role_name}' not found"}), HTTPStatus.NOT_FOUND
    user.roles.append(role)
    db.session.commit()
    return user_schema.jsonify(user)


@admin_bp.route("/teams", methods=["GET"])
@jwt_required()
@admin_required
def list_teams():
    query = Team.query
    return jsonify(paginate_query(query, teams_schema))


@admin_bp.route("/system-settings", methods=["GET"])
@jwt_required()
@admin_required
def list_system_settings():
    """List all system settings, including alert thresholds for system health (e.g., alert_threshold_failed_webhooks, alert_threshold_error_rate, alert_threshold_low_credits)."""
    settings = SystemSetting.query.all()
    return system_settings_schema.jsonify(settings)


@admin_bp.route("/system-settings", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Updated system setting")
def create_or_update_system_setting():
    """Create or update a system setting. For system alerts, use keys like alert_threshold_failed_webhooks, alert_threshold_error_rate, alert_threshold_low_credits. Value type must match the threshold type (int or float)."""
    data = request.get_json() or {}
    key = data.get("key")
    value = data.get("value")
    setting = SystemSetting.query.get(key)
    if setting:
        setting.value = value
    else:
        setting = SystemSetting(key=key, value=value)
        db.session.add(setting)
    db.session.commit()
    return system_setting_schema.jsonify(setting)


@admin_bp.route("/audit-logs", methods=["GET"])
@jwt_required()
@admin_required
def list_audit_logs():
    query = AuditLog.query.order_by(AuditLog.created_at.desc())
    return jsonify(paginate_query(query, audit_logs_schema))


@admin_bp.route("/users/by-role", methods=["GET"])
@jwt_required()
@admin_required
def list_users_by_role():
    """List all users with a given role (role param)."""
    role_name = request.args.get("role")
    if not role_name:
        return jsonify({"msg": "Missing role parameter"}), HTTPStatus.BAD_REQUEST
    query = User.query.join(User.roles).filter(Role.name == role_name)
    return jsonify(paginate_query(query, users_schema))


# -----------------------------------------------------------------------------
# Developer Admin Dashboard
# -----------------------------------------------------------------------------


@admin_bp.route("/developer/accounts", methods=["GET"])
@jwt_required()
@developer_required
def list_developer_accounts():
    # Return only users with 'Developer' or 'Developer Admin' roles
    developer_roles = ["Developer", "Developer Admin"]
    developers = User.query.join(User.roles).filter(Role.name.in_(developer_roles)).all()
    return users_schema.jsonify(developers)


@admin_bp.route("/developer/resource-allocation", methods=["GET"])
@jwt_required()
@developer_required
def get_resource_allocation():
    user_id = request.args.get("user_id")
    team_id = request.args.get("team_id")
    if user_id:
        target = User.query.get(user_id)
    elif team_id:
        target = Team.query.get(team_id)
    else:
        return jsonify({"msg": "user_id or team_id required"}), HTTPStatus.BAD_REQUEST
    if not target:
        return jsonify({"msg": "User or Team not found"}), HTTPStatus.NOT_FOUND
    # Storage usage: count OTPs, notes, folders, etc.
    if isinstance(target, User):
        storage_used = {
            "otps": len(target.otps),
            "notes": len(getattr(target, "authored_notes", [])),
            "folders": len(getattr(target, "folders", [])),
        }
        resource_limits = {
            "api_quota_per_month": target.api_quota_per_month,
            "api_rate_limit_per_second": target.api_rate_limit_per_second,
            "storage_limit": getattr(target, "storage_limit", None),
        }
    else:  # Team
        storage_used = {
            "otps": len(target.otps),
            "notes": sum(len(member.user.authored_notes) for member in target.members if hasattr(member.user, "authored_notes")),
            "folders": len(getattr(target, "folders", [])),
        }
        resource_limits = {
            "api_quota_per_month": target.api_quota_per_month,
            "api_rate_limit_per_second": target.api_rate_limit_per_second,
            "storage_limit": getattr(target, "storage_limit", None),
        }
    return jsonify({
        "resource_limits": resource_limits,
        "storage_used": storage_used
    })


@admin_bp.route("/developer/resource-allocation", methods=["POST"])
@jwt_required()
@developer_required
@audit_log("Updated resource allocation")
def allocate_resources():
    data = request.get_json() or {}
    user_id = data.get("user_id")
    team_id = data.get("team_id")
    target = None
    if user_id:
        target = User.query.get(user_id)
    elif team_id:
        target = Team.query.get(team_id)
    if not target:
        return jsonify({"msg": "User or Team not found"}), HTTPStatus.NOT_FOUND
    # Set quotas and limits
    target.api_quota_per_month = data.get("api_quota_per_month", target.api_quota_per_month)
    target.api_rate_limit_per_second = data.get("api_rate_limit_per_second", target.api_rate_limit_per_second)
    # Storage limit (number of OTPs + notes + folders)
    if hasattr(target, "storage_limit"):
        target.storage_limit = data.get("storage_limit", getattr(target, "storage_limit", None))
    db.session.commit()
    return jsonify({"msg": "Resources allocated successfully"})


# -----------------------------------------------------------------------------
# Sales & Marketing Dashboards
# -----------------------------------------------------------------------------


@admin_bp.route("/sales/coupons", methods=["POST"])
@jwt_required()
@sales_required
def create_coupon():
    data = request.get_json() or {}
    coupon = coupon_schema.load(data)
    db.session.add(coupon)
    db.session.commit()
    return coupon_schema.jsonify(coupon), HTTPStatus.CREATED


@admin_bp.route("/sales/coupons", methods=["GET"])
@jwt_required()
@sales_required
def list_coupons():
    coupons = Coupon.query.all()
    return coupons_schema.jsonify(coupons)


@admin_bp.route("/sales/analytics", methods=["GET"])
@jwt_required()
@sales_required
def get_sales_analytics():
    # Placeholder for real analytics
    return jsonify(
        {
            "coupons_used": Coupon.query.with_entities(func.sum(Coupon.use_count)).scalar(),
            "total_discounts": 0,  # Would require more complex tracking
        }
    )


@admin_bp.route("/marketing/invitations", methods=["POST"])
@jwt_required()
@marketing_required
def create_invitation():
    data = request.get_json() or {}
    invitation = invitation_schema.load(data)
    db.session.add(invitation)
    db.session.commit()
    return invitation_schema.jsonify(invitation), HTTPStatus.CREATED


@admin_bp.route("/marketing/invitations", methods=["GET"])
@jwt_required()
@marketing_required
def list_invitations():
    invitations = Invitation.query.all()
    return invitations_schema.jsonify(invitations)


# ----------------------------------------------------------------------------
# Campaign Management (Marketing/Sales Admin)
# ----------------------------------------------------------------------------

@admin_bp.route("/campaigns", methods=["POST"])
@jwt_required()
@marketing_required
@audit_log("Created campaign")
def create_campaign():
    data = request.get_json() or {}
    # Validate required fields
    if not data.get("name") or not data.get("type"):
        return jsonify({"msg": "Missing required fields: name, type"}), HTTPStatus.BAD_REQUEST
    # Check for duplicate name
    if Campaign.query.filter_by(name=data["name"]).first():
        return jsonify({"msg": "Campaign name already exists"}), HTTPStatus.CONFLICT
    campaign = campaign_schema.load(data)
    db.session.add(campaign)
    db.session.commit()
    return campaign_schema.jsonify(campaign), HTTPStatus.CREATED

@admin_bp.route("/campaigns/<int:campaign_id>/performance", methods=["GET"])
@jwt_required()
@marketing_required
def get_campaign_performance(campaign_id: int):
    campaign = Campaign.query.get_or_404(campaign_id)
    # Aggregate performance metrics
    signups = User.query.filter_by(campaign_id=campaign_id).count()
    referrals = Referral.query.filter_by(campaign_id=campaign_id).count()
    invitations = Invitation.query.filter_by(campaign_id=campaign_id).count()
    coupons = Coupon.query.filter_by(campaign_id=campaign_id).count()
    coupon_uses = db.session.query(db.func.sum(Coupon.use_count)).filter(Coupon.campaign_id == campaign_id).scalar() or 0
    revenue = db.session.query(db.func.sum(Payment.amount)).join(User, Payment.user_id == User.id).filter(User.campaign_id == campaign_id).scalar() or 0
    # Attribution: first/last touch, linear, etc. (simple: count signups, referrals, coupon uses)
    return jsonify({
        "campaign": campaign_schema.dump(campaign),
        "metrics": {
            "signups": signups,
            "referrals": referrals,
            "invitations": invitations,
            "coupons": coupons,
            "coupon_uses": coupon_uses,
            "revenue": revenue,
        },
        "referrals": referrals_schema.dump(Referral.query.filter_by(campaign_id=campaign_id).all()),
    })

# -----------------------------------------------------------------------------
# Admin In-App Notifications
# -----------------------------------------------------------------------------

@admin_bp.route("/notifications", methods=["GET"])
@jwt_required()
@admin_required
def list_notifications():
    """List in-app notifications for the current admin (Super/Developer Admin)."""
    user_id = get_jwt_identity()
    current_user = User.query.get_or_404(user_id)
    # Only show notifications for this user or for their team(s) if admin
    query = Notification.query.filter(
        (Notification.user_id == current_user.id) |
        (Notification.team_id.in_([tm.team_id for tm in current_user.teams]))
    )
    # Optional filters
    if request.args.get("unread") == "true":
        query = query.filter_by(is_read=False)
    if request.args.get("severity"):
        query = query.filter_by(severity=request.args["severity"])
    query = query.order_by(Notification.created_at.desc())
    return notifications_schema.jsonify(query.limit(100).all())

@admin_bp.route("/notifications/mark-read", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Marked notifications as read")
def mark_notifications_read():
    data = request.get_json() or {}
    ids = data.get("ids", [])
    if not ids:
        return {"msg": "No notification IDs provided"}, 400
    Notification.query.filter(Notification.id.in_(ids)).update({"is_read": True}, synchronize_session=False)
    db.session.commit()
    return {"msg": "Notifications marked as read"}

@admin_bp.route("/notifications/dismiss", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Dismissed notifications")
def dismiss_notifications():
    data = request.get_json() or {}
    ids = data.get("ids", [])
    if not ids:
        return {"msg": "No notification IDs provided"}, 400
    Notification.query.filter(Notification.id.in_(ids)).update({"is_dismissed": True}, synchronize_session=False)
    db.session.commit()
    return {"msg": "Notifications dismissed"}
