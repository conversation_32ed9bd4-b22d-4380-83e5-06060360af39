"""Authentication endpoints (register, login, etc.)."""
from __future__ import annotations

from http import HTTPStatus

from flask import Blueprint, jsonify, request, url_for, current_app
from flask_jwt_extended import create_access_token, get_jwt_identity, jwt_required

from ..extensions import bcrypt, db
from ..models import (
    User, OTP, Folder, Note, APIKey, AuditLog, TeamMember, Ticket, TicketMessage, Payment, OTPShare, FolderShare, WebhookConfig, SecondaryPasswordAttempt, ShareAccessLog, Invitation, Coupon, Campaign, Referral
)
from ..schemas import (
    UserSchema, OTPSchema, FolderSchema, NoteSchema, APIKeySchema, AuditLogSchema, TeamMemberSchema, TicketSchema, PaymentSchema, OTPShareSchema, FolderShareSchema, WebhookConfigSchema, SecondaryPasswordAttemptSchema
)

from ..sso import oauth, get_user_info_from_provider, get_available_providers
from ..routes.webhooks import trigger_user_registered_webhook
from ..crypto_utils import encrypt_with_password, decrypt_with_password
from ..decorators import validate_json, validate_query_params, rate_limit_by_ip, api_quota_required, log_api_request, security_headers
from ..schemas.requests import (
    RegisterRequestSchema, LoginRequestSchema, SetSecondaryPasswordRequestSchema,
    ExportVaultRequestSchema, ImportVaultRequestSchema
)
import base64
from sqlalchemy.orm import joinedload

auth_bp = Blueprint("auth", __name__)

user_schema = UserSchema()
otp_schema = OTPSchema()
otps_schema = OTPSchema(many=True)
folder_schema = FolderSchema()
folders_schema = FolderSchema(many=True)
note_schema = NoteSchema()
notes_schema = NoteSchema(many=True)

export_schemas = {
    'user': UserSchema(),
    'otps': OTPSchema(many=True),
    'folders': FolderSchema(many=True),
    'notes': NoteSchema(many=True),
    'api_keys': APIKeySchema(many=True),
    'audit_logs': AuditLogSchema(many=True),
    'teams': TeamMemberSchema(many=True),
    'tickets': TicketSchema(many=True),
    'payments': PaymentSchema(many=True),
    'otp_shares': OTPShareSchema(many=True),
    'folder_shares': FolderShareSchema(many=True),
    'webhook_configs': WebhookConfigSchema(many=True),
    'secondary_password_attempts': SecondaryPasswordAttemptSchema(many=True),
}


@auth_bp.route("/login/<string:provider>")
def social_login(provider: str):
    """Redirect to the social login provider."""
    redirect_uri = url_for("auth.authorize", provider=provider, _external=True)
    return oauth.create_client(provider).authorize_redirect(redirect_uri)


@auth_bp.route("/authorize/<string:provider>")
def authorize(provider: str):
    """Handle the authorization callback from the social login provider."""
    try:
        client = oauth.create_client(provider)
        token = client.authorize_access_token()
        
        # Get user info using the enhanced provider system
        user_info = get_user_info_from_provider(provider, token)
        
        if not user_info.get("email"):
            return jsonify({"msg": "Email not provided by OAuth provider"}), HTTPStatus.BAD_REQUEST

        # Find or create the user
        user = User.query.filter_by(email=user_info["email"]).first()
        if not user:
            user = User(
                email=user_info["email"],
                first_name=user_info.get("first_name", ""),
                last_name=user_info.get("last_name", ""),
                password_hash="",  # Social login users don't have a password
                credits=current_app.config.get("DEFAULT_USER_CREDITS", 100)
            )
            db.session.add(user)
            db.session.commit()

        access_token = create_access_token(identity=user.id)
        trigger_user_registered_webhook(user.id)
        return jsonify({
            "access_token": access_token,
            "user": user_schema.dump(user)
        })
    
    except Exception as e:
        return jsonify({"msg": f"OAuth authorization failed: {str(e)}"}), HTTPStatus.BAD_REQUEST


@auth_bp.post("/register")
@rate_limit_by_ip("5 per minute")
@validate_json(RegisterRequestSchema)
@log_api_request
@security_headers
def register(validated_data):  # noqa: D401
    """Register a new user."""
    email = validated_data["email"]
    password = validated_data["password"]
    secondary_password = validated_data.get("secondary_password")
    encrypted_dek = validated_data.get("encrypted_dek")
    invitation_code = validated_data.get("invitation_code")
    coupon_code = validated_data.get("coupon_code")
    referrer_id = validated_data.get("referrer_id")

    if User.query.filter_by(email=email).first():
        return jsonify({"msg": "Email already registered"}), HTTPStatus.CONFLICT

    pw_hash = bcrypt.generate_password_hash(password).decode()
    user = User(email=email, password_hash=pw_hash)

    if secondary_password and encrypted_dek:
        user.secondary_password_hash = bcrypt.generate_password_hash(
            secondary_password
        ).decode()
        user.encrypted_dek = encrypted_dek

    # Campaign/referral logic
    campaign_id = None
    invitation = None
    coupon = None
    method = None
    if invitation_code:
        invitation = Invitation.query.filter_by(code=invitation_code, is_active=True).first()
        if invitation:
            campaign_id = invitation.campaign_id
            method = "invitation"
            invitation.status = "accepted"
            invitation.is_active = False
    elif coupon_code:
        coupon = Coupon.query.filter_by(code=coupon_code, is_active=True).first()
        if coupon:
            campaign_id = coupon.campaign_id
            method = "coupon"
            coupon.use_count += 1
    if campaign_id:
        user.campaign_id = campaign_id
    if referrer_id:
        user.referred_by_id = referrer_id
    db.session.add(user)
    db.session.flush()  # get user.id
    # Create referral record if applicable
    if campaign_id or referrer_id or invitation or coupon:
        referral = Referral(
            referred_user_id=user.id,
            referrer_user_id=referrer_id,
            campaign_id=campaign_id,
            invitation_id=invitation.id if invitation else None,
            coupon_id=coupon.id if coupon else None,
            method=method,
        )
        db.session.add(referral)
    db.session.commit()
    trigger_user_registered_webhook(user.id)
    return user_schema.jsonify(user), HTTPStatus.CREATED


@auth_bp.post("/login")
@rate_limit_by_ip("10 per minute")
@validate_json(LoginRequestSchema)
@log_api_request
@security_headers
def login(validated_data):  # noqa: D401
    """Authenticate user and return JWT."""
    email = validated_data["email"]
    password = validated_data["password"]

    user: User | None = User.query.filter_by(email=email).first()
    if user is None or not bcrypt.check_password_hash(user.password_hash, password or ""):
        return jsonify({"msg": "Invalid credentials"}), HTTPStatus.UNAUTHORIZED

    access_token = create_access_token(identity=user.id)
    return jsonify({"access_token": access_token})


@auth_bp.post("/set-secondary-password")
@jwt_required()
@validate_json(SetSecondaryPasswordRequestSchema)
@api_quota_required
@log_api_request
@security_headers
def set_secondary_password(validated_data):
    """Set or update the secondary password and encrypted DEK for the user."""
    user_id: int = get_jwt_identity()  # type: ignore
    current_user: User = User.query.get_or_404(user_id)

    secondary_password = validated_data["secondary_password"]
    encrypted_dek = validated_data["encrypted_dek"]

    current_user.secondary_password_hash = bcrypt.generate_password_hash(
        secondary_password
    ).decode()
    current_user.encrypted_dek = encrypted_dek
    db.session.commit()

    return jsonify({"msg": "Secondary password updated successfully."}), HTTPStatus.OK


@auth_bp.route("/providers", methods=["GET"])
def list_oauth_providers():
    """List available OAuth providers."""
    providers = get_available_providers(current_app)
    return jsonify({"providers": providers})


@auth_bp.route("/me", methods=["GET"])
@jwt_required()
def get_current_user_info():
    """Get current user information."""
    user_id: int = get_jwt_identity()  # type: ignore
    current_user: User = User.query.get_or_404(user_id)
    return user_schema.jsonify(current_user)


@auth_bp.route("/me/export-vault", methods=["POST"])
@jwt_required()
def export_vault():
    """Export all user-owned OTPs, folders, and notes as an encrypted JSON blob."""
    user_id: int = get_jwt_identity()  # type: ignore
    current_user: User = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    secondary_password = data.get("secondary_password")
    if not secondary_password:
        return jsonify({"msg": "secondary_password is required"}), HTTPStatus.BAD_REQUEST
    # Query all user-owned data
    otps = OTP.query.filter_by(owner_id=current_user.id).all()
    folders = Folder.query.filter_by(owner_id=current_user.id).all()
    notes = Note.query.join(OTP).filter(OTP.owner_id == current_user.id).all()
    # Serialize
    export_data = {
        "otps": otps_schema.dump(otps),
        "folders": folders_schema.dump(folders),
        "notes": notes_schema.dump(notes),
    }
    # Encrypt
    plaintext = jsonify(export_data).data  # bytes
    salt, ciphertext = encrypt_with_password(plaintext, secondary_password)
    # Return base64-encoded for safe transport
    return jsonify({
        "salt": base64.b64encode(salt).decode(),
        "encrypted_vault": base64.b64encode(ciphertext).decode(),
    })


@auth_bp.route("/me/import-vault", methods=["POST"])
@jwt_required()
def import_vault():
    """Import and restore user vault from an encrypted JSON blob. Replaces all user vault data."""
    user_id: int = get_jwt_identity()  # type: ignore
    current_user: User = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    secondary_password = data.get("secondary_password")
    salt_b64 = data.get("salt")
    encrypted_vault_b64 = data.get("encrypted_vault")
    if not secondary_password or not salt_b64 or not encrypted_vault_b64:
        return jsonify({"msg": "secondary_password, salt, and encrypted_vault are required"}), HTTPStatus.BAD_REQUEST
    try:
        salt = base64.b64decode(salt_b64)
        ciphertext = base64.b64decode(encrypted_vault_b64)
        plaintext = decrypt_with_password(salt, ciphertext, secondary_password)
    except Exception as e:
        return jsonify({"msg": f"Decryption failed: {str(e)}"}), HTTPStatus.BAD_REQUEST
    try:
        import json
        vault_data = json.loads(plaintext)
    except Exception as e:
        return jsonify({"msg": f"Invalid vault data: {str(e)}"}), HTTPStatus.BAD_REQUEST
    # Replace all user vault data
    OTP.query.filter_by(owner_id=current_user.id).delete()
    Folder.query.filter_by(owner_id=current_user.id).delete()
    # Notes are deleted via OTP cascade
    db.session.commit()
    # Restore folders
    folder_objs = []
    for folder in vault_data.get("folders", []):
        folder_obj = Folder(
            name=folder["name"],
            description=folder.get("description"),
            owner_id=current_user.id,
            parent_id=folder.get("parent_id"),
        )
        db.session.add(folder_obj)
        folder_objs.append(folder_obj)
    db.session.commit()
    # Map old folder IDs to new ones
    old_to_new_folder_ids = {}
    for old, new in zip([f["id"] for f in vault_data.get("folders", [])], [f.id for f in folder_objs]):
        old_to_new_folder_ids[old] = new
    # Restore OTPs
    otp_objs = []
    for otp in vault_data.get("otps", []):
        folder_id = old_to_new_folder_ids.get(otp.get("folder_id")) if otp.get("folder_id") else None
        otp_obj = OTP(
            name=otp["name"],
            secret_encrypted=otp["secret_encrypted"],
            algorithm=otp.get("algorithm", "SHA1"),
            digits=otp.get("digits", 6),
            period=otp.get("period", 30),
            counter=otp.get("counter"),
            metadata=otp.get("metadata", {}),
            owner_id=current_user.id,
            folder_id=folder_id,
        )
        db.session.add(otp_obj)
        otp_objs.append(otp_obj)
    db.session.commit()
    # Map old OTP IDs to new ones
    old_to_new_otp_ids = {}
    for old, new in zip([o["id"] for o in vault_data.get("otps", [])], [o.id for o in otp_objs]):
        old_to_new_otp_ids[old] = new
    # Restore notes
    for note in vault_data.get("notes", []):
        otp_id = old_to_new_otp_ids.get(note.get("otp_id"))
        if not otp_id:
            continue  # skip notes for OTPs not restored
        note_obj = Note(
            otp_id=otp_id,
            author_id=current_user.id,
            title=note.get("title", ""),
            content_encrypted=note["content_encrypted"],
        )
        db.session.add(note_obj)
    db.session.commit()
    # Audit log
    from ..routes.admin_helpers import audit_log
    audit_log("Imported vault")(lambda: None)()  # Log the import
    return jsonify({"msg": "Vault imported successfully."})


@auth_bp.route("/users/me/export-data", methods=["GET"])
@jwt_required()
def export_personal_data():
    """Export all personal data for the current user (GDPR/CCPA)."""
    user_id = get_jwt_identity()
    user = User.query.get_or_404(user_id)
    # Gather all related data
    otps = OTP.query.filter_by(owner_id=user.id).all()
    folders = Folder.query.filter_by(owner_id=user.id).all()
    notes = Note.query.filter_by(author_id=user.id).all()
    api_keys = APIKey.query.filter_by(user_id=user.id).all()
    audit_logs = AuditLog.query.filter_by(user_id=user.id).all()
    teams = TeamMember.query.filter_by(user_id=user.id).all()
    tickets = Ticket.query.filter_by(user_id=user.id).all()
    payments = Payment.query.filter_by(user_id=user.id).all()
    otp_shares = OTPShare.query.filter((OTPShare.shared_by_user_id==user.id)|(OTPShare.shared_with_user_id==user.id)).all()
    folder_shares = FolderShare.query.filter((FolderShare.shared_by_user_id==user.id)|(FolderShare.shared_with_user_id==user.id)).all()
    webhook_configs = WebhookConfig.query.filter_by(user_id=user.id).all()
    secondary_password_attempts = SecondaryPasswordAttempt.query.filter_by(user_id=user.id).all()
    # Compose export
    export = {
        'user': export_schemas['user'].dump(user),
        'otps': export_schemas['otps'].dump(otps),
        'folders': export_schemas['folders'].dump(folders),
        'notes': export_schemas['notes'].dump(notes),
        'api_keys': export_schemas['api_keys'].dump(api_keys),
        'audit_logs': export_schemas['audit_logs'].dump(audit_logs),
        'teams': export_schemas['teams'].dump(teams),
        'tickets': export_schemas['tickets'].dump(tickets),
        'payments': export_schemas['payments'].dump(payments),
        'otp_shares': export_schemas['otp_shares'].dump(otp_shares),
        'folder_shares': export_schemas['folder_shares'].dump(folder_shares),
        'webhook_configs': export_schemas['webhook_configs'].dump(webhook_configs),
        'secondary_password_attempts': export_schemas['secondary_password_attempts'].dump(secondary_password_attempts),
    }
    # Audit log
    log = AuditLog(user_id=user.id, action="gdpr_export", details={"exported": True})
    db.session.add(log)
    db.session.commit()
    return jsonify(export)


@auth_bp.route("/users/me/request-account-deletion", methods=["POST"])
@jwt_required()
def request_account_deletion():
    """Request immediate and irreversible account deletion (GDPR/CCPA)."""
    user_id = get_jwt_identity()
    user = User.query.get_or_404(user_id)
    # Audit log before deletion
    log = AuditLog(user_id=user.id, action="gdpr_delete_requested", details={"requested": True})
    db.session.add(log)
    db.session.commit()
    # Delete all user-related data
    OTP.query.filter_by(owner_id=user.id).delete()
    Folder.query.filter_by(owner_id=user.id).delete()
    Note.query.filter_by(author_id=user.id).delete()
    APIKey.query.filter_by(user_id=user.id).delete()
    AuditLog.query.filter_by(user_id=user.id).delete()
    TeamMember.query.filter_by(user_id=user.id).delete()
    Ticket.query.filter_by(user_id=user.id).delete()
    TicketMessage.query.filter_by(user_id=user.id).delete()
    Payment.query.filter_by(user_id=user.id).delete()
    OTPShare.query.filter((OTPShare.shared_by_user_id==user.id)|(OTPShare.shared_with_user_id==user.id)).delete(synchronize_session=False)
    FolderShare.query.filter((FolderShare.shared_by_user_id==user.id)|(FolderShare.shared_with_user_id==user.id)).delete(synchronize_session=False)
    WebhookConfig.query.filter_by(user_id=user.id).delete()
    SecondaryPasswordAttempt.query.filter_by(user_id=user.id).delete()
    ShareAccessLog.query.filter_by(accessed_by_user_id=user.id).delete()
    # Finally, delete the user
    db.session.delete(user)
    db.session.commit()
    return jsonify({"msg": "Your account and all associated data have been permanently deleted."})
