"""Request monitoring middleware for observability and metrics."""
from __future__ import annotations

import time
from flask import request, g, current_app
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request

from ..extensions import api_requests_total, api_request_duration


class RequestMonitoringMiddleware:
    """Middleware for comprehensive request monitoring and metrics."""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the middleware with the Flask app."""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """Record request start time and gather request info."""
        g.start_time = time.time()
        
        # Get user ID if authenticated
        g.user_id = None
        try:
            verify_jwt_in_request(optional=True)
            g.user_id = get_jwt_identity()
        except:
            pass
        
        # Store request info for logging
        g.request_info = {
            'method': request.method,
            'path': request.path,
            'endpoint': request.endpoint,
            'ip_address': self._get_client_ip(),
            'user_agent': request.headers.get('User-Agent', ''),
            'content_length': request.content_length or 0,
        }
    
    def after_request(self, response):
        """Record request metrics and log request completion."""
        if not hasattr(g, 'start_time'):
            return response
        
        # Calculate request duration
        duration = time.time() - g.start_time
        
        # Update Prometheus metrics
        api_requests_total.labels(
            method=request.method,
            endpoint=request.endpoint or 'unknown'
        ).inc()
        
        api_request_duration.observe(duration)
        
        # Log request completion
        self._log_request_completion(response, duration)
        
        # Add performance headers
        response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        return response
    
    def _get_client_ip(self) -> str:
        """Get the real client IP address."""
        # Check for forwarded IP first (for reverse proxies)
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or 'unknown'
    
    def _log_request_completion(self, response, duration: float):
        """Log request completion with structured data."""
        log_data = {
            'request_id': getattr(g, 'request_id', 'unknown'),
            'user_id': getattr(g, 'user_id', None),
            'method': request.method,
            'path': request.path,
            'endpoint': request.endpoint,
            'status_code': response.status_code,
            'duration_ms': round(duration * 1000, 2),
            'ip_address': self._get_client_ip(),
            'user_agent': request.headers.get('User-Agent', ''),
            'content_length': getattr(g, 'request_info', {}).get('content_length', 0),
            'response_size': len(response.get_data()) if hasattr(response, 'get_data') else 0,
        }
        
        # Log at appropriate level based on status code
        if response.status_code >= 500:
            current_app.logger.error(f"Request completed with server error", extra=log_data)
        elif response.status_code >= 400:
            current_app.logger.warning(f"Request completed with client error", extra=log_data)
        elif duration > 5.0:  # Log slow requests
            current_app.logger.warning(f"Slow request detected", extra=log_data)
        else:
            current_app.logger.info(f"Request completed", extra=log_data)
