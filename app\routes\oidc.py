"""OpenID Connect (OIDC) SSO endpoints for generic identity providers."""
from __future__ import annotations

from http import HTTPStatus
import secrets
import base64
import hashlib
from urllib.parse import urlencode, parse_qs

from flask import Blueprint, jsonify, request, redirect, url_for, current_app, session
from flask_jwt_extended import create_access_token
import requests

from ..extensions import db
from ..models import User
from ..schemas import UserSchema
from ..routes.webhooks import trigger_user_registered_webhook
from ..routes.admin_helpers import audit_log

oidc_bp = Blueprint("oidc", __name__, url_prefix="/api/sso/oidc")

user_schema = UserSchema()


def get_oidc_config():
    """Get OIDC configuration from app config."""
    return {
        "discovery_url": current_app.config.get("OIDC_DISCOVERY_URL"),
        "client_id": current_app.config.get("OIDC_CLIENT_ID"),
        "client_secret": current_app.config.get("OIDC_CLIENT_SECRET"),
    }


def discover_oidc_endpoints(discovery_url: str) -> dict:
    """Discover OIDC endpoints from the discovery document."""
    try:
        response = requests.get(discovery_url, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        current_app.logger.error(f"Failed to discover OIDC endpoints: {e}")
        raise ValueError(f"OIDC discovery failed: {e}")


def generate_pkce_challenge():
    """Generate PKCE code verifier and challenge for secure OIDC flow."""
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    code_challenge = base64.urlsafe_b64encode(
        hashlib.sha256(code_verifier.encode('utf-8')).digest()
    ).decode('utf-8').rstrip('=')
    return code_verifier, code_challenge


@oidc_bp.route("/login", methods=["POST"])
def oidc_login():
    """Initiate OIDC login flow with PKCE."""
    config = get_oidc_config()
    
    if not all([config["discovery_url"], config["client_id"], config["client_secret"]]):
        return jsonify({"msg": "OIDC not configured"}), HTTPStatus.BAD_REQUEST
    
    try:
        # Discover OIDC endpoints
        discovery_doc = discover_oidc_endpoints(config["discovery_url"])
        authorization_endpoint = discovery_doc.get("authorization_endpoint")
        
        if not authorization_endpoint:
            return jsonify({"msg": "Authorization endpoint not found"}), HTTPStatus.BAD_REQUEST
        
        # Generate PKCE parameters
        code_verifier, code_challenge = generate_pkce_challenge()
        state = secrets.token_urlsafe(32)
        nonce = secrets.token_urlsafe(32)
        
        # Store PKCE and state in session for verification
        session["oidc_code_verifier"] = code_verifier
        session["oidc_state"] = state
        session["oidc_nonce"] = nonce
        
        # Build authorization URL
        redirect_uri = url_for("oidc.oidc_callback", _external=True)
        auth_params = {
            "response_type": "code",
            "client_id": config["client_id"],
            "redirect_uri": redirect_uri,
            "scope": "openid profile email",
            "state": state,
            "nonce": nonce,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
        }
        
        auth_url = f"{authorization_endpoint}?{urlencode(auth_params)}"
        
        return redirect(auth_url)
        
    except Exception as e:
        current_app.logger.error(f"OIDC login initiation failed: {e}")
        return jsonify({"msg": f"OIDC login failed: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR


@oidc_bp.route("/callback", methods=["GET"])
def oidc_callback():
    """Handle OIDC authorization callback."""
    config = get_oidc_config()
    
    # Verify state parameter
    state = request.args.get("state")
    if not state or state != session.get("oidc_state"):
        return jsonify({"msg": "Invalid state parameter"}), HTTPStatus.BAD_REQUEST
    
    # Get authorization code
    code = request.args.get("code")
    if not code:
        error = request.args.get("error")
        error_description = request.args.get("error_description", "Unknown error")
        return jsonify({
            "msg": f"OIDC authorization failed: {error} - {error_description}"
        }), HTTPStatus.BAD_REQUEST
    
    try:
        # Discover token endpoint
        discovery_doc = discover_oidc_endpoints(config["discovery_url"])
        token_endpoint = discovery_doc.get("token_endpoint")
        userinfo_endpoint = discovery_doc.get("userinfo_endpoint")
        
        if not token_endpoint:
            return jsonify({"msg": "Token endpoint not found"}), HTTPStatus.BAD_REQUEST
        
        # Exchange code for tokens
        redirect_uri = url_for("oidc.oidc_callback", _external=True)
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": redirect_uri,
            "client_id": config["client_id"],
            "client_secret": config["client_secret"],
            "code_verifier": session.get("oidc_code_verifier"),
        }
        
        token_response = requests.post(
            token_endpoint,
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        token_response.raise_for_status()
        tokens = token_response.json()
        
        access_token = tokens.get("access_token")
        id_token = tokens.get("id_token")
        
        if not access_token:
            return jsonify({"msg": "No access token received"}), HTTPStatus.BAD_REQUEST
        
        # Get user info
        user_info = None
        if userinfo_endpoint:
            userinfo_response = requests.get(
                userinfo_endpoint,
                headers={"Authorization": f"Bearer {access_token}"},
                timeout=10
            )
            if userinfo_response.status_code == 200:
                user_info = userinfo_response.json()
        
        # If no userinfo endpoint, try to decode ID token (simplified)
        if not user_info and id_token:
            # In production, you should properly verify the JWT signature
            import json
            try:
                # This is a simplified approach - in production, use proper JWT verification
                payload = id_token.split('.')[1]
                # Add padding if needed
                payload += '=' * (4 - len(payload) % 4)
                user_info = json.loads(base64.urlsafe_b64decode(payload))
            except Exception as e:
                current_app.logger.warning(f"Failed to decode ID token: {e}")
        
        if not user_info:
            return jsonify({"msg": "Failed to get user information"}), HTTPStatus.BAD_REQUEST
        
        # Extract user details
        email = user_info.get("email")
        if not email:
            return jsonify({"msg": "Email not provided by OIDC provider"}), HTTPStatus.BAD_REQUEST
        
        first_name = user_info.get("given_name", "")
        last_name = user_info.get("family_name", "")
        
        # Find or create user
        user = User.query.filter_by(email=email).first()
        if not user:
            user = User(
                email=email,
                first_name=first_name,
                last_name=last_name,
                password_hash="",  # OIDC users don't have a password
                credits=current_app.config.get("DEFAULT_USER_CREDITS", 100)
            )
            db.session.add(user)
            db.session.commit()
            
            # Trigger webhook for new user registration
            trigger_user_registered_webhook(user.id)
        
        # Create JWT token
        jwt_token = create_access_token(identity=user.id)
        
        # Audit log
        from ..models import AuditLog
        audit_entry = AuditLog(
            user_id=user.id,
            action="OIDC Login",
            details={
                "provider": config["discovery_url"],
                "ip": request.remote_addr,
                "user_agent": request.headers.get("User-Agent"),
                "oidc_sub": user_info.get("sub"),
            }
        )
        db.session.add(audit_entry)
        db.session.commit()
        
        # Clear session data
        session.pop("oidc_code_verifier", None)
        session.pop("oidc_state", None)
        session.pop("oidc_nonce", None)
        
        return jsonify({
            "access_token": jwt_token,
            "user": user_schema.dump(user)
        })
        
    except requests.RequestException as e:
        current_app.logger.error(f"OIDC token exchange failed: {e}")
        return jsonify({"msg": f"OIDC authentication failed: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR
    except Exception as e:
        current_app.logger.error(f"OIDC callback processing failed: {e}")
        return jsonify({"msg": f"OIDC authentication failed: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR


@oidc_bp.route("/logout", methods=["POST"])
def oidc_logout():
    """Initiate OIDC logout flow."""
    config = get_oidc_config()
    
    if not config["discovery_url"]:
        return jsonify({"msg": "OIDC not configured"}), HTTPStatus.BAD_REQUEST
    
    try:
        # Discover logout endpoint
        discovery_doc = discover_oidc_endpoints(config["discovery_url"])
        end_session_endpoint = discovery_doc.get("end_session_endpoint")
        
        if not end_session_endpoint:
            # Provider doesn't support logout - just clear local session
            return jsonify({"msg": "Logged out locally"}), HTTPStatus.OK
        
        # Build logout URL
        post_logout_redirect_uri = request.json.get("post_logout_redirect_uri") if request.json else None
        logout_params = {
            "client_id": config["client_id"],
        }
        
        if post_logout_redirect_uri:
            logout_params["post_logout_redirect_uri"] = post_logout_redirect_uri
        
        logout_url = f"{end_session_endpoint}?{urlencode(logout_params)}"
        
        return jsonify({"logout_url": logout_url})
        
    except Exception as e:
        current_app.logger.error(f"OIDC logout failed: {e}")
        return jsonify({"msg": f"OIDC logout failed: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR


@oidc_bp.route("/config", methods=["GET"])
def get_oidc_config_info():
    """Get OIDC configuration information (public data only)."""
    config = get_oidc_config()
    
    if not config["discovery_url"]:
        return jsonify({"configured": False}), HTTPStatus.OK
    
    try:
        discovery_doc = discover_oidc_endpoints(config["discovery_url"])
        
        return jsonify({
            "configured": True,
            "issuer": discovery_doc.get("issuer"),
            "authorization_endpoint": discovery_doc.get("authorization_endpoint"),
            "scopes_supported": discovery_doc.get("scopes_supported", []),
            "response_types_supported": discovery_doc.get("response_types_supported", []),
        })
        
    except Exception as e:
        current_app.logger.error(f"Failed to get OIDC config: {e}")
        return jsonify({"configured": False, "error": str(e)}), HTTPStatus.INTERNAL_SERVER_ERROR
