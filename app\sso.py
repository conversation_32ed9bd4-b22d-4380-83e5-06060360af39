"""SSO and social login configuration using Authlib."""
from __future__ import annotations

from authlib.integrations.flask_client import OAuth
from flask import Flask

oauth = OAuth()


def init_app(app: Flask) -> None:
    """Initialize the OAuth registry with multiple providers."""
    oauth.init_app(app)
    
    # Google OAuth
    if app.config.get("GOOGLE_CLIENT_ID") and app.config.get("GOOGLE_CLIENT_SECRET"):
        oauth.register(
            name="google",
            client_id=app.config["GOOGLE_CLIENT_ID"],
            client_secret=app.config["GOOGLE_CLIENT_SECRET"],
            server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
            client_kwargs={
                "scope": "openid email profile"
            }
        )
    
    # GitHub OAuth
    if app.config.get("GITHUB_CLIENT_ID") and app.config.get("GITHUB_CLIENT_SECRET"):
        oauth.register(
            name="github",
            client_id=app.config["GITHUB_CLIENT_ID"],
            client_secret=app.config["GITHUB_CLIENT_SECRET"],
            access_token_url="https://github.com/login/oauth/access_token",
            authorize_url="https://github.com/login/oauth/authorize",
            api_base_url="https://api.github.com/",
            client_kwargs={"scope": "user:email"}
        )
    
    # Microsoft OAuth (Azure AD)
    if app.config.get("MICROSOFT_CLIENT_ID") and app.config.get("MICROSOFT_CLIENT_SECRET"):
        oauth.register(
            name="microsoft",
            client_id=app.config["MICROSOFT_CLIENT_ID"],
            client_secret=app.config["MICROSOFT_CLIENT_SECRET"],
            server_metadata_url="https://login.microsoftonline.com/common/v2.0/.well-known/openid_configuration",
            client_kwargs={
                "scope": "openid email profile"
            }
        )
    
    # LinkedIn OAuth
    if app.config.get("LINKEDIN_CLIENT_ID") and app.config.get("LINKEDIN_CLIENT_SECRET"):
        oauth.register(
            name="linkedin",
            client_id=app.config["LINKEDIN_CLIENT_ID"],
            client_secret=app.config["LINKEDIN_CLIENT_SECRET"],
            access_token_url="https://www.linkedin.com/oauth/v2/accessToken",
            authorize_url="https://www.linkedin.com/oauth/v2/authorization",
            api_base_url="https://api.linkedin.com/v2/",
            client_kwargs={"scope": "r_liteprofile r_emailaddress"}
        )
    
    # Facebook OAuth
    if app.config.get("FACEBOOK_CLIENT_ID") and app.config.get("FACEBOOK_CLIENT_SECRET"):
        oauth.register(
            name="facebook",
            client_id=app.config["FACEBOOK_CLIENT_ID"],
            client_secret=app.config["FACEBOOK_CLIENT_SECRET"],
            access_token_url="https://graph.facebook.com/oauth/access_token",
            authorize_url="https://www.facebook.com/dialog/oauth",
            api_base_url="https://graph.facebook.com/",
            client_kwargs={"scope": "email"}
        )
    
    # Generic OIDC Provider
    if app.config.get("OIDC_CLIENT_ID") and app.config.get("OIDC_CLIENT_SECRET") and app.config.get("OIDC_DISCOVERY_URL"):
        oauth.register(
            name="oidc",
            client_id=app.config["OIDC_CLIENT_ID"],
            client_secret=app.config["OIDC_CLIENT_SECRET"],
            server_metadata_url=app.config["OIDC_DISCOVERY_URL"],
            client_kwargs={
                "scope": "openid email profile"
            }
        )


def get_user_info_from_provider(provider: str, token: dict) -> dict:
    """Extract user information from different OAuth providers."""
    client = oauth.create_client(provider)
    
    if provider == "google":
        user_info = client.parse_id_token(token)
        return {
            "email": user_info.get("email"),
            "first_name": user_info.get("given_name"),
            "last_name": user_info.get("family_name"),
            "provider": "google",
            "provider_id": user_info.get("sub")
        }
    
    elif provider == "github":
        # GitHub requires separate API calls for user info
        resp = client.get("user", token=token)
        user_data = resp.json()
        
        # Get primary email
        email_resp = client.get("user/emails", token=token)
        emails = email_resp.json()
        primary_email = next((email["email"] for email in emails if email["primary"]), None)
        
        return {
            "email": primary_email or user_data.get("email"),
            "first_name": user_data.get("name", "").split(" ")[0] if user_data.get("name") else "",
            "last_name": " ".join(user_data.get("name", "").split(" ")[1:]) if user_data.get("name") else "",
            "provider": "github",
            "provider_id": str(user_data.get("id"))
        }
    
    elif provider == "microsoft":
        user_info = client.parse_id_token(token)
        return {
            "email": user_info.get("email") or user_info.get("preferred_username"),
            "first_name": user_info.get("given_name"),
            "last_name": user_info.get("family_name"),
            "provider": "microsoft",
            "provider_id": user_info.get("sub")
        }
    
    elif provider == "linkedin":
        # LinkedIn requires separate API calls
        profile_resp = client.get("people/~:(id,firstName,lastName)", token=token)
        profile_data = profile_resp.json()
        
        email_resp = client.get("people/~/emailAddress?q=members&projection=(elements*(handle~))", token=token)
        email_data = email_resp.json()
        
        email = None
        if email_data.get("elements"):
            email = email_data["elements"][0].get("handle~", {}).get("emailAddress")
        
        return {
            "email": email,
            "first_name": profile_data.get("firstName", {}).get("localized", {}).get("en_US", ""),
            "last_name": profile_data.get("lastName", {}).get("localized", {}).get("en_US", ""),
            "provider": "linkedin",
            "provider_id": profile_data.get("id")
        }
    
    elif provider == "facebook":
        resp = client.get("me?fields=id,email,first_name,last_name", token=token)
        user_data = resp.json()
        
        return {
            "email": user_data.get("email"),
            "first_name": user_data.get("first_name"),
            "last_name": user_data.get("last_name"),
            "provider": "facebook",
            "provider_id": user_data.get("id")
        }
    
    elif provider == "oidc":
        user_info = client.parse_id_token(token)
        return {
            "email": user_info.get("email"),
            "first_name": user_info.get("given_name") or user_info.get("name", "").split(" ")[0],
            "last_name": user_info.get("family_name") or " ".join(user_info.get("name", "").split(" ")[1:]),
            "provider": "oidc",
            "provider_id": user_info.get("sub")
        }
    
    else:
        raise ValueError(f"Unsupported provider: {provider}")


def get_available_providers(app: Flask) -> list[str]:
    """Get list of configured OAuth providers."""
    providers = []
    
    if app.config.get("GOOGLE_CLIENT_ID") and app.config.get("GOOGLE_CLIENT_SECRET"):
        providers.append("google")
    
    if app.config.get("GITHUB_CLIENT_ID") and app.config.get("GITHUB_CLIENT_SECRET"):
        providers.append("github")
    
    if app.config.get("MICROSOFT_CLIENT_ID") and app.config.get("MICROSOFT_CLIENT_SECRET"):
        providers.append("microsoft")
    
    if app.config.get("LINKEDIN_CLIENT_ID") and app.config.get("LINKEDIN_CLIENT_SECRET"):
        providers.append("linkedin")
    
    if app.config.get("FACEBOOK_CLIENT_ID") and app.config.get("FACEBOOK_CLIENT_SECRET"):
        providers.append("facebook")
    
    if (app.config.get("OIDC_CLIENT_ID") and 
        app.config.get("OIDC_CLIENT_SECRET") and 
        app.config.get("OIDC_DISCOVERY_URL")):
        providers.append("oidc")
    
    return providers
