"""Flask application factory and blueprint registration for Cloud OTP backend."""
from __future__ import annotations

from flask import Flask

from .config import get_config
from .extensions import bcrypt, db, jwt, ma, migrate, celery, limiter, setup_json_logging
from .services.analytics_service import AnalyticsService


def create_app(config_name: str | None = None) -> Flask:
    """Application factory used by Flask to create app instances.

    Using a factory pattern makes the application easy to test and configure
    for multiple environments (development, production, etc.).
    """
    app = Flask(__name__)
    
    # Load configuration based on environment
    config_class = get_config(config_name)
    app.config.from_object(config_class)

    # ---------------------------------------------------------------------
    # Logging configuration (structured JSON for production)
    import logging
    from logging.handlers import RotatingFileHandler
    log_level = app.config.get("LOG_LEVEL", "INFO")
    log_file = app.config.get("LOG_FILE", "cloud_otp.log")
    
    # Use structured JSON logging in production
    if app.config.get("FLASK_ENV") == "production":
        json_handler = setup_json_logging()
        json_handler.setLevel(log_level)
        app.logger.addHandler(json_handler)
    else:
        # Traditional logging for development
        formatter = logging.Formatter('[%(asctime)s] %(levelname)s in %(module)s: %(message)s')
        
        # File handler
        file_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=5)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        app.logger.addHandler(file_handler)
        
        # Console handler (for Docker/Gunicorn)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        app.logger.addHandler(console_handler)
    
    app.logger.setLevel(log_level)

    # ---------------------------------------------------------------------
    # Initialise Flask extensions
    # ---------------------------------------------------------------------
    db.init_app(app)
    ma.init_app(app)
    migrate.init_app(app, db)
    bcrypt.init_app(app)
    jwt.init_app(app)
    limiter.init_app(app)

    celery.config_from_object(app.config, namespace="CELERY")

    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    app.extensions["celery"] = celery

    # New: Initialize mail, CORS, healthcheck, flask-healthz
    from .extensions import mail, cors, health, envdump, healthz_ext
    mail.init_app(app)
    cors.init_app(app, origins=app.config.get("CORS_ORIGINS", ["*"]))

    # Sentry initialization (if DSN provided)
    import sentry_sdk
    from sentry_sdk.integrations.flask import FlaskIntegration
    if app.config.get("SENTRY_DSN"):
        sentry_sdk.init(
            dsn=app.config["SENTRY_DSN"],
            environment=app.config.get("SENTRY_ENVIRONMENT", "production"),
            traces_sample_rate=app.config.get("SENTRY_TRACES_SAMPLE_RATE", 0.1),
            integrations=[FlaskIntegration()]
        )

    # Register healthcheck endpoints
    app.add_url_rule("/healthcheck", "healthcheck", view_func=lambda: health.run())
    app.add_url_rule("/environment", "environment", view_func=lambda: envdump.run())

    # Register flask-healthz endpoints (Kubernetes liveness/readiness)
    app.register_blueprint(healthz_ext(), url_prefix="/healthz")

    # Register Prometheus metrics endpoint
    from prometheus_client import make_wsgi_app
    from werkzeug.middleware.dispatcher import DispatcherMiddleware
    app.wsgi_app = DispatcherMiddleware(app.wsgi_app, {"/metrics": make_wsgi_app()})

    from . import sso
    sso.init_app(app)

    # Register error handlers
    from .error_handlers import register_error_handlers, register_api_exception_handler
    register_error_handlers(app)
    register_api_exception_handler(app)
    
    # Register after_request handlers
    from .decorators import add_quota_headers
    app.after_request(add_quota_headers)

    # Initialize API documentation
    from .api_docs import init_api_docs
    init_api_docs(app)

    # ---------------------------------------------------------------------
    # Register blueprints (modular route definitions)
    # ---------------------------------------------------------------------
    from .routes.auth import auth_bp  # pylint: disable=import-outside-toplevel
    from .routes.otp import otp_bp  # pylint: disable=import-outside-toplevel
    from .routes.admin import admin_bp  # pylint: disable=import-outside-toplevel
    from .routes.api_keys import api_key_bp # pylint: disable=import-outside-toplevel
    from .routes.teams import team_bp  # pylint: disable=import-outside-toplevel
    from .routes.support import support_bp  # pylint: disable=import-outside-toplevel
    from .routes.payments import payments_bp  # pylint: disable=import-outside-toplevel
    from .routes.invitations import invitations_bp  # pylint: disable=import-outside-toplevel
    from .routes.coupons import coupons_bp  # pylint: disable=import-outside-toplevel
    from .routes.secondary_password import secondary_password_bp  # pylint: disable=import-outside-toplevel
    from .routes.webhooks import webhooks_bp  # pylint: disable=import-outside-toplevel
    from .routes.analytics import analytics_bp  # pylint: disable=import-outside-toplevel
    from .routes.rate_limiting import rate_limiting_bp  # pylint: disable=import-outside-toplevel
    from .routes.oidc import oidc_bp  # pylint: disable=import-outside-toplevel
    from .routes.saml import saml_bp  # pylint: disable=import-outside-toplevel

    app.register_blueprint(auth_bp, url_prefix="/api/auth")
    app.register_blueprint(otp_bp, url_prefix="/api/otp")
    app.register_blueprint(admin_bp, url_prefix="/api/admin")
    app.register_blueprint(api_key_bp, url_prefix="/api/keys")
    app.register_blueprint(team_bp, url_prefix="/api/teams")
    app.register_blueprint(support_bp, url_prefix="/api/support")
    app.register_blueprint(payments_bp, url_prefix="/api/payments")
    app.register_blueprint(invitations_bp, url_prefix="/api/invitations")
    app.register_blueprint(coupons_bp, url_prefix="/api/coupons")
    app.register_blueprint(secondary_password_bp, url_prefix="/api/secondary-password")
    app.register_blueprint(webhooks_bp, url_prefix="/api/webhooks")
    app.register_blueprint(analytics_bp, url_prefix="/api/analytics")
    app.register_blueprint(rate_limiting_bp, url_prefix="/api/rate-limiting")
    app.register_blueprint(oidc_bp)  # Already has url_prefix in blueprint
    app.register_blueprint(saml_bp)  # Already has url_prefix in blueprint

    # Register custom CLI commands
    from .cli import cli_commands  # pylint: disable=import-outside-toplevel

    for command in cli_commands:
        app.cli.add_command(command)

    # ---------------------------------------------------------------------
    # Shell context for interactive debugging with `flask shell`
    # ---------------------------------------------------------------------
    @app.shell_context_processor  # type: ignore[misc]
    def _make_shell_context() -> dict[str, object]:  # noqa: D401
        """Provide objects to the interactive shell context."""
        from . import models  # pylint: disable=import-outside-toplevel

        return {
            "db": db,
            **{name: cls for name, cls in models.__dict__.items() if isinstance(cls, type)},
        }

    return app
