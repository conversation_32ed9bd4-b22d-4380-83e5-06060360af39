from flask import Blueprint, request, jsonify, Response, redirect, url_for, current_app, make_response
from http import HTTPStatus
from ..models import SAMLIdentityProvider, Team, User, db
from ..schemas import SAMLIdentityProviderSchema, UserSchema
from sqlalchemy.exc import SQLAlchemyError
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from saml2 import BINDING_HTTP_POST, BINDING_HTTP_REDIRECT
from saml2.config import SPConfig
from saml2.client import Saml2Client
from saml2.metadata import entity_descriptor
from saml2.response import StatusError
from saml2.s_utils import UnsupportedBinding
import base64
import logging
import os
from werkzeug.utils import secure_filename
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization
from .admin_helpers import admin_required, team_admin_required, audit_log

saml_bp = Blueprint("saml", __name__, url_prefix="/api")

saml_idp_schema = SAMLIdentityProviderSchema()
user_schema = UserSchema()
logger = logging.getLogger(__name__)

# --- Config ---
SAML_KEY_DIR = os.environ.get("SAML_KEY_DIR", "./saml_keys")
os.makedirs(SAML_KEY_DIR, exist_ok=True)

# --- Helper: Save uploaded PEM file securely ---
def save_pem_file(file, team_id, kind):
    filename = secure_filename(f"team{team_id}_{kind}.pem")
    path = os.path.join(SAML_KEY_DIR, filename)
    file.save(path)
    return path

# --- Helper: Generate RSA keypair ---
def generate_rsa_keypair(team_id):
    private_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
    priv_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption(),
    )
    pub_pem = private_key.public_key().public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    )
    priv_path = os.path.join(SAML_KEY_DIR, f"team{team_id}_sp.key.pem")
    cert_path = os.path.join(SAML_KEY_DIR, f"team{team_id}_sp.cert.pem")
    with open(priv_path, "wb") as f:
        f.write(priv_pem)
    with open(cert_path, "wb") as f:
        f.write(pub_pem)
    return priv_path, cert_path

# --- Helper: Build pysaml2 config from DB model ---
def build_sp_config(team_id=None, idp: SAMLIdentityProvider = None):
    # Get SAML config for the team
    if not idp and team_id:
        idp = SAMLIdentityProvider.query.filter_by(team_id=team_id, is_active=True).first()
    if not idp:
        raise ValueError("No active SAML IdP config for this team")
    acs_url = url_for("saml.saml_acs", _external=True)
    metadata_url = url_for("saml.saml_metadata", _external=True)
    entity_id = current_app.config.get("SAML_SP_ENTITY_ID") or metadata_url
    key_path = os.path.join(SAML_KEY_DIR, f"team{team_id}_sp.key.pem")
    cert_path = os.path.join(SAML_KEY_DIR, f"team{team_id}_sp.cert.pem")
    sp_config = {
        "entityid": entity_id,
        "service": {
            "sp": {
                "endpoints": {
                    "assertion_consumer_service": [
                        (acs_url, BINDING_HTTP_POST),
                    ],
                    "single_logout_service": [
                        (idp.slo_url, BINDING_HTTP_REDIRECT),
                    ] if idp.slo_url else [],
                },
                "allow_unsolicited": idp.allow_unsolicited if hasattr(idp, 'allow_unsolicited') else True,
                "authn_requests_signed": False,
                "logout_requests_signed": False,
                "want_assertions_signed": True,
                "want_response_signed": True,
                "name_id_format": ["urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"],
            }
        },
        "metadata": {
            "inline": [
                f"""
                <EntityDescriptor entityID=\"{idp.entity_id}\" xmlns=\"urn:oasis:names:tc:SAML:2.0:metadata\">
                    <IDPSSODescriptor protocolSupportEnumeration=\"urn:oasis:names:tc:SAML:2.0:protocol\">
                        <KeyDescriptor use=\"signing\">
                            <KeyInfo xmlns=\"http://www.w3.org/2000/09/xmldsig#\">
                                <X509Data><X509Certificate>{idp.x509_cert.strip().replace('-----BEGIN CERTIFICATE-----','').replace('-----END CERTIFICATE-----','').replace('\n','')}</X509Certificate></X509Data>
                            </KeyInfo>
                        </KeyDescriptor>
                        <SingleSignOnService Binding=\"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect\" Location=\"{idp.sso_url}\"/>
                        {f'<SingleLogoutService Binding=\"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect\" Location=\"{idp.slo_url}\"/>' if idp.slo_url else ''}
                    </IDPSSODescriptor>
                </EntityDescriptor>
                """
            ]
        },
        "key_file": key_path if os.path.exists(key_path) else current_app.config.get("SAML_SP_KEY_FILE"),
        "cert_file": cert_path if os.path.exists(cert_path) else current_app.config.get("SAML_SP_CERT_FILE"),
        "xmlsec_binary": current_app.config.get("XMLSEC_BINARY", "/usr/bin/xmlsec1"),
        "debug": current_app.config.get("DEBUG", False),
    }
    return SPConfig().load(sp_config)

# --- SAML SP Metadata ---
@saml_bp.route("/sso/saml/metadata", methods=["GET"])
def saml_metadata():
    team_id = request.args.get("team_id", type=int)
    if not team_id:
        return jsonify({"msg": "team_id required"}), HTTPStatus.BAD_REQUEST
    try:
        idp = SAMLIdentityProvider.query.filter_by(team_id=team_id, is_active=True).first()
        if not idp:
            return jsonify({"msg": "No SAML IdP config for this team"}), HTTPStatus.NOT_FOUND
        sp_config = build_sp_config(team_id, idp)
        ed = entity_descriptor(sp_config)
        xml = ed.to_string().decode()
        return Response(xml, mimetype="application/xml")
    except Exception as e:
        logger.exception("Failed to generate SAML metadata")
        return jsonify({"msg": f"Failed to generate SAML metadata: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR

# --- SAML SSO Login Initiation ---
@saml_bp.route("/sso/saml/login", methods=["POST"])
def saml_login():
    data = request.get_json() or {}
    team_id = data.get("team_id")
    relay_state = data.get("relay_state")
    if not team_id:
        return jsonify({"msg": "team_id required"}), HTTPStatus.BAD_REQUEST
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id, is_active=True).first()
    if not idp:
        return jsonify({"msg": "No SAML IdP config for this team"}), HTTPStatus.NOT_FOUND
    try:
        sp_config = build_sp_config(team_id, idp)
        client = Saml2Client(config=sp_config)
        # Prepare AuthnRequest
        reqid, info = client.prepare_for_authenticate(relay_state=relay_state)
        for key, value in info["headers"]:
            if key.lower() == "location":
                return redirect(value)
        return jsonify({"msg": "Failed to initiate SAML login"}), HTTPStatus.INTERNAL_SERVER_ERROR
    except Exception as e:
        logger.exception("Failed to initiate SAML login")
        return jsonify({"msg": f"Failed to initiate SAML login: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR

# --- SAML Assertion Consumer Service (ACS) ---
@saml_bp.route("/sso/saml/acs", methods=["POST"])
def saml_acs():
    saml_response = request.form.get("SAMLResponse")
    relay_state = request.form.get("RelayState")
    team_id = request.args.get("team_id", type=int) or request.form.get("team_id", type=int)
    if not saml_response or not team_id:
        return jsonify({"msg": "SAMLResponse and team_id required"}), HTTPStatus.BAD_REQUEST
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id, is_active=True).first()
    if not idp:
        return jsonify({"msg": "No SAML IdP config for this team"}), HTTPStatus.NOT_FOUND
    try:
        sp_config = build_sp_config(team_id, idp)
        client = Saml2Client(config=sp_config)
        authn_response = client.parse_authn_request_response(saml_response, BINDING_HTTP_POST)
        if not authn_response.is_authenticated():
            return jsonify({"msg": "SAML authentication failed"}), HTTPStatus.UNAUTHORIZED
        # Extract user info from assertion
        attributes = authn_response.ava or {}
        name_id = authn_response.get_subject().text if authn_response.get_subject() else None
        email = attributes.get(idp.attribute_mapping.get("email", "EmailAddress"), [None])[0] or name_id
        first_name = attributes.get(idp.attribute_mapping.get("first_name", "FirstName"), [""])[0]
        last_name = attributes.get(idp.attribute_mapping.get("last_name", "LastName"), [""])[0]
        if not email:
            return jsonify({"msg": "No email in SAML assertion"}), HTTPStatus.BAD_REQUEST
        # Provision user if not exists
        user = User.query.filter_by(email=email).first()
        if not user:
            user = User(email=email, first_name=first_name, last_name=last_name, password_hash="", credits=current_app.config.get("DEFAULT_USER_CREDITS", 100))
            db.session.add(user)
            db.session.commit()
        # Issue JWT
        access_token = create_access_token(identity=user.id)
        # Optionally, add user to team if not already a member
        from ..models import TeamMember
        if not TeamMember.query.filter_by(team_id=team_id, user_id=user.id).first():
            db.session.add(TeamMember(team_id=team_id, user_id=user.id, role="member"))
            db.session.commit()
        # --- Audit log for SAML login ---
        log_saml_event(
            user.id,
            team_id,
            "SAML Login",
            {
                "ip": request.remote_addr,
                "user_agent": request.user_agent.string,
                "relay_state": relay_state,
                "attributes": attributes,
            },
        )
        # Return token and user info
        return jsonify({
            "access_token": access_token,
            "user": user_schema.dump(user),
            "relay_state": relay_state
        })
    except StatusError as e:
        logger.warning(f"SAML status error: {e}")
        return jsonify({"msg": f"SAML status error: {str(e)}"}), HTTPStatus.UNAUTHORIZED
    except UnsupportedBinding as e:
        logger.warning(f"SAML unsupported binding: {e}")
        return jsonify({"msg": f"SAML unsupported binding: {str(e)}"}), HTTPStatus.BAD_REQUEST
    except Exception as e:
        logger.exception("Failed to process SAML assertion")
        return jsonify({"msg": f"Failed to process SAML assertion: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR

# --- SAML IdP Config CRUD (per team) ---
@saml_bp.route("/teams/<int:team_id>/saml-idp", methods=["GET"])
def get_saml_idp(team_id):
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id).first()
    if not idp:
        return jsonify({"msg": "SAML IdP config not found"}), HTTPStatus.NOT_FOUND
    return saml_idp_schema.jsonify(idp)

@saml_bp.route("/teams/<int:team_id>/saml-idp", methods=["POST"])
def create_saml_idp(team_id):
    if SAMLIdentityProvider.query.filter_by(team_id=team_id).first():
        return jsonify({"msg": "SAML IdP config already exists for this team"}), HTTPStatus.CONFLICT
    data = request.get_json() or {}
    data["team_id"] = team_id
    idp = saml_idp_schema.load(data)
    db.session.add(idp)
    db.session.commit()
    return saml_idp_schema.jsonify(idp), HTTPStatus.CREATED

@saml_bp.route("/teams/<int:team_id>/saml-idp", methods=["PATCH"])
def update_saml_idp(team_id):
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id).first()
    if not idp:
        return jsonify({"msg": "SAML IdP config not found"}), HTTPStatus.NOT_FOUND
    data = request.get_json() or {}
    for key, value in data.items():
        if hasattr(idp, key):
            setattr(idp, key, value)
    db.session.commit()
    return saml_idp_schema.jsonify(idp)

@saml_bp.route("/teams/<int:team_id>/saml-idp", methods=["DELETE"])
def delete_saml_idp(team_id):
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id).first()
    if not idp:
        return jsonify({"msg": "SAML IdP config not found"}), HTTPStatus.NOT_FOUND
    db.session.delete(idp)
    db.session.commit()
    return "", HTTPStatus.NO_CONTENT

# --- SP Key/Cert Upload (Admin/Team Admin) ---
@saml_bp.route("/teams/<int:team_id>/saml-sp-key", methods=["POST"])
@jwt_required()
@team_admin_required
@audit_log("Uploaded SAML SP key/cert")
def upload_sp_key(team_id):
    if "key" not in request.files or "cert" not in request.files:
        return jsonify({"msg": "Both key and cert files required (PEM)"}), HTTPStatus.BAD_REQUEST
    key_file = request.files["key"]
    cert_file = request.files["cert"]
    key_path = save_pem_file(key_file, team_id, "sp.key")
    cert_path = save_pem_file(cert_file, team_id, "sp.cert")
    # Optionally, update DB or config pointer here
    return jsonify({"msg": "SP key/cert uploaded", "key_path": key_path, "cert_path": cert_path})

# --- SP Key/Cert Generation (Admin/Team Admin) ---
@saml_bp.route("/teams/<int:team_id>/saml-sp-key/generate", methods=["POST"])
@jwt_required()
@team_admin_required
@audit_log("Generated SAML SP key/cert")
def generate_sp_key(team_id):
    priv_path, cert_path = generate_rsa_keypair(team_id)
    return jsonify({"msg": "SP key/cert generated", "key_path": priv_path, "cert_path": cert_path})

# --- SAML SLO (Single Logout) ---
@saml_bp.route("/sso/saml/slo", methods=["POST"])
def saml_slo():
    data = request.get_json() or {}
    team_id = data.get("team_id")
    if not team_id:
        return jsonify({"msg": "team_id required"}), HTTPStatus.BAD_REQUEST
    idp = SAMLIdentityProvider.query.filter_by(team_id=team_id, is_active=True).first()
    if not idp or not idp.slo_url:
        return jsonify({"msg": "No SLO URL configured for this team"}), HTTPStatus.NOT_FOUND
    try:
        sp_config = build_sp_config(team_id, idp)
        client = Saml2Client(config=sp_config)
        # Prepare SLO request
        reqid, info = client.prepare_for_logout()
        # --- Audit log for SAML logout ---
        user_id = get_jwt_identity()
        log_saml_event(
            user_id,
            team_id,
            "SAML Logout",
            {
                "ip": request.remote_addr,
                "user_agent": request.user_agent.string,
            },
        )
        for key, value in info["headers"]:
            if key.lower() == "location":
                return redirect(value)
        return jsonify({"msg": "Failed to initiate SAML logout"}), HTTPStatus.INTERNAL_SERVER_ERROR
    except Exception as e:
        logger.exception("Failed to initiate SAML SLO")
        return jsonify({"msg": f"Failed to initiate SAML SLO: {str(e)}"}), HTTPStatus.INTERNAL_SERVER_ERROR

# --- Audit Logging for SAML Login/Logout ---
def log_saml_event(user_id, team_id, action, details=None):
    from ..models import AuditLog
    log = AuditLog(
        user_id=user_id,
        team_id=team_id,
        action=action,
        details=details or {},
    )
    db.session.add(log)
    db.session.commit()

# Patch into ACS and SLO endpoints
# In saml_acs(): after successful login:
#   log_saml_event(user.id, team_id, "SAML Login", {"ip": request.remote_addr, ...})
# In saml_slo(): after successful logout:
#   log_saml_event(get_jwt_identity(), team_id, "SAML Logout", {"ip": request.remote_addr, ...})

# --- OpenAPI doc stubs for new endpoints (to be added in api_docs.py) ---
# /api/teams/{team_id}/saml-sp-key (POST): Upload SP key/cert
# /api/teams/{team_id}/saml-sp-key/generate (POST): Generate SP key/cert
# /api/sso/saml/slo (POST): SAML Single Logout 