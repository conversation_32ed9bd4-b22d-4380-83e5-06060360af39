"""Request validation schemas using Marshmallow for all API endpoints."""
from __future__ import annotations

from marshmallow import Schema, fields, validate, validates_schema, ValidationError
from datetime import datetime


class BaseRequestSchema(Schema):
    """Base schema with common validation rules."""
    
    class Meta:
        unknown = "EXCLUDE"  # Ignore unknown fields for security


class RegisterRequestSchema(BaseRequestSchema):
    """Schema for user registration requests."""
    
    email = fields.Email(required=True, validate=validate.Length(max=255))
    password = fields.Str(
        required=True, 
        validate=[
            validate.Length(min=8, max=128),
            validate.Regexp(
                r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
                error="Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character"
            )
        ]
    )
    first_name = fields.Str(validate=validate.Length(max=100))
    last_name = fields.Str(validate=validate.Length(max=100))
    secondary_password = fields.Str(validate=validate.Length(min=16, max=256))
    encrypted_dek = fields.Str()
    invitation_code = fields.Str(validate=validate.Length(max=50))
    coupon_code = fields.Str(validate=validate.Length(max=50))
    referrer_id = fields.Int()


class LoginRequestSchema(BaseRequestSchema):
    """Schema for user login requests."""
    
    email = fields.Email(required=True)
    password = fields.Str(required=True)


class SetSecondaryPasswordRequestSchema(BaseRequestSchema):
    """Schema for setting secondary password."""
    
    secondary_password = fields.Str(
        required=True,
        validate=validate.Length(min=16, max=256)
    )
    encrypted_dek = fields.Str(required=True)
    current_secondary_password = fields.Str()


class VerifySecondaryPasswordRequestSchema(BaseRequestSchema):
    """Schema for verifying secondary password."""
    
    secondary_password = fields.Str(required=True)


class CreateOTPRequestSchema(BaseRequestSchema):
    """Schema for creating OTP entries."""
    
    name = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    secret_encrypted = fields.Str(required=True)
    algorithm = fields.Str(
        validate=validate.OneOf(["SHA1", "SHA256", "SHA512"]),
        missing="SHA1"
    )
    digits = fields.Int(
        validate=validate.Range(min=4, max=10),
        missing=6
    )
    period = fields.Int(
        validate=validate.Range(min=15, max=300),
        missing=30
    )
    counter = fields.Int(validate=validate.Range(min=0))
    metadata = fields.Dict(missing=dict)
    folder_id = fields.Int()
    team_id = fields.Int()


class UpdateOTPRequestSchema(BaseRequestSchema):
    """Schema for updating OTP entries."""
    
    name = fields.Str(validate=validate.Length(min=1, max=255))
    secret_encrypted = fields.Str()
    algorithm = fields.Str(validate=validate.OneOf(["SHA1", "SHA256", "SHA512"]))
    digits = fields.Int(validate=validate.Range(min=4, max=10))
    period = fields.Int(validate=validate.Range(min=15, max=300))
    counter = fields.Int(validate=validate.Range(min=0))
    metadata = fields.Dict()
    folder_id = fields.Int()


class ShareOTPRequestSchema(BaseRequestSchema):
    """Schema for sharing OTP entries."""
    
    share_type = fields.Str(
        required=True,
        validate=validate.OneOf(["user", "team", "public"])
    )
    email = fields.Email()  # Required for user shares
    team_id = fields.Int()  # Required for team shares
    permission = fields.Str(
        validate=validate.OneOf(["read", "write", "admin"]),
        missing="read"
    )
    expires_at = fields.DateTime()
    password_hash = fields.Str()  # For password-protected public shares
    access_limit = fields.Int(validate=validate.Range(min=1))
    
    @validates_schema
    def validate_share_requirements(self, data, **kwargs):
        """Validate share type specific requirements."""
        share_type = data.get("share_type")
        
        if share_type == "user" and not data.get("email"):
            raise ValidationError("Email is required for user shares")
        
        if share_type == "team" and not data.get("team_id"):
            raise ValidationError("Team ID is required for team shares")


class CreateFolderRequestSchema(BaseRequestSchema):
    """Schema for creating folders."""
    
    name = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    description = fields.Str(validate=validate.Length(max=1000))
    team_id = fields.Int()
    parent_id = fields.Int()


class UpdateFolderRequestSchema(BaseRequestSchema):
    """Schema for updating folders."""
    
    name = fields.Str(validate=validate.Length(min=1, max=255))
    description = fields.Str(validate=validate.Length(max=1000))
    parent_id = fields.Int()


class CreateTeamRequestSchema(BaseRequestSchema):
    """Schema for creating teams."""
    
    name = fields.Str(required=True, validate=validate.Length(min=1, max=255))


class InviteTeamMemberRequestSchema(BaseRequestSchema):
    """Schema for inviting team members."""
    
    email = fields.Email(required=True)
    role = fields.Str(
        validate=validate.OneOf(["admin", "member", "read-only"]),
        missing="member"
    )


class AcceptTeamInvitationRequestSchema(BaseRequestSchema):
    """Schema for accepting team invitations."""
    
    token = fields.Str(required=True)


class ChangeTeamMemberRoleRequestSchema(BaseRequestSchema):
    """Schema for changing team member roles."""
    
    role = fields.Str(
        required=True,
        validate=validate.OneOf(["admin", "member", "read-only"])
    )


class CreateTicketRequestSchema(BaseRequestSchema):
    """Schema for creating support tickets."""
    
    subject = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    message = fields.Str(required=True, validate=validate.Length(min=1, max=5000))


class ReplyToTicketRequestSchema(BaseRequestSchema):
    """Schema for replying to support tickets."""
    
    message = fields.Str(required=True, validate=validate.Length(min=1, max=5000))


class CreateWebhookConfigRequestSchema(BaseRequestSchema):
    """Schema for creating webhook configurations."""
    
    target_url = fields.Url(required=True)
    team_id = fields.Int()
    secret = fields.Str(validate=validate.Length(max=128))
    is_active = fields.Bool(missing=True)
    subscribed_events = fields.Str(missing="*")


class UpdateWebhookConfigRequestSchema(BaseRequestSchema):
    """Schema for updating webhook configurations."""
    
    target_url = fields.Url()
    secret = fields.Str(validate=validate.Length(max=128))
    is_active = fields.Bool()
    subscribed_events = fields.Str()


class CreateCouponRequestSchema(BaseRequestSchema):
    """Schema for creating coupons."""
    
    code = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    discount_type = fields.Str(
        required=True,
        validate=validate.OneOf(["percentage", "fixed_amount", "credits"])
    )
    value = fields.Int(required=True, validate=validate.Range(min=1))
    expires_at = fields.DateTime()
    max_uses = fields.Int(validate=validate.Range(min=1))
    campaign_id = fields.Int()


class UpdateCouponRequestSchema(BaseRequestSchema):
    """Schema for updating coupons."""
    
    discount_type = fields.Str(validate=validate.OneOf(["percentage", "fixed_amount", "credits"]))
    value = fields.Int(validate=validate.Range(min=1))
    expires_at = fields.DateTime()
    max_uses = fields.Int(validate=validate.Range(min=1))
    is_active = fields.Bool()


class CreateInvitationRequestSchema(BaseRequestSchema):
    """Schema for creating invitations."""
    
    code = fields.Str(validate=validate.Length(min=1, max=50))
    team_id = fields.Int()
    email = fields.Email()
    expires_at = fields.DateTime()
    campaign_id = fields.Int()


class CreateCampaignRequestSchema(BaseRequestSchema):
    """Schema for creating campaigns."""
    
    name = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    type = fields.Str(
        required=True,
        validate=validate.OneOf(["marketing", "sales", "referral"])
    )
    description = fields.Str(validate=validate.Length(max=1000))
    start_date = fields.DateTime()
    end_date = fields.DateTime()
    metadata = fields.Dict(missing=dict)
    
    @validates_schema
    def validate_dates(self, data, **kwargs):
        """Validate that end_date is after start_date."""
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        
        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date")


class SAMLIdPConfigRequestSchema(BaseRequestSchema):
    """Schema for SAML Identity Provider configuration."""
    
    idp_name = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    entity_id = fields.Str(required=True, validate=validate.Length(min=1, max=512))
    sso_url = fields.Url(required=True)
    x509_cert = fields.Str(required=True)
    slo_url = fields.Url()
    x509_cert_signing = fields.Str()
    attribute_mapping = fields.Dict(missing=dict)
    is_active = fields.Bool(missing=True)


class PaginationRequestSchema(BaseRequestSchema):
    """Schema for pagination parameters."""
    
    page = fields.Int(validate=validate.Range(min=1), missing=1)
    per_page = fields.Int(validate=validate.Range(min=1, max=100), missing=20)


class DateRangeRequestSchema(BaseRequestSchema):
    """Schema for date range filters."""
    
    start_date = fields.DateTime()
    end_date = fields.DateTime()
    period = fields.Str(validate=validate.OneOf(["today", "week", "month", "quarter", "year"]))
    
    @validates_schema
    def validate_date_range(self, data, **kwargs):
        """Validate date range parameters."""
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        period = data.get("period")
        
        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date")
        
        if period and (start_date or end_date):
            raise ValidationError("Cannot specify both period and custom date range")


class AddCreditsRequestSchema(BaseRequestSchema):
    """Schema for adding credits to accounts."""
    
    amount = fields.Int(required=True, validate=validate.Range(min=1))
    reason = fields.Str(validate=validate.Length(max=255))


class UpdateResourceAllocationRequestSchema(BaseRequestSchema):
    """Schema for updating resource allocations."""
    
    resources = fields.List(
        fields.Dict(keys=fields.Str(), values=fields.Raw()),
        required=True
    )
    
    @validates_schema
    def validate_resources(self, data, **kwargs):
        """Validate resource allocation format."""
        resources = data.get("resources", [])
        
        for resource in resources:
            if not isinstance(resource, dict):
                raise ValidationError("Each resource must be an object")
            
            if "resource_type" not in resource or "limit" not in resource:
                raise ValidationError("Each resource must have resource_type and limit")
            
            if not isinstance(resource["limit"], int) or resource["limit"] < 0:
                raise ValidationError("Resource limit must be a non-negative integer")


class ExportVaultRequestSchema(BaseRequestSchema):
    """Schema for vault export requests."""
    
    secondary_password = fields.Str(required=True)


class ImportVaultRequestSchema(BaseRequestSchema):
    """Schema for vault import requests."""
    
    secondary_password = fields.Str(required=True)
    salt = fields.Str(required=True)
    encrypted_vault = fields.Str(required=True)
