"""Endpoints for handling Paddle payment webhooks."""
from __future__ import annotations

from http import HTTPStatus

from flask import Blueprint, jsonify, request

from ..extensions import db
from ..models import Payment, User, Team
from ..routes.webhooks import trigger_payment_received_webhook

payments_bp = Blueprint("payments", __name__)


@payments_bp.route("/paddle-webhook", methods=["POST"])
def paddle_webhook():
    """Handle incoming webhooks from Paddle."""
    data = request.get_json() or {}
    event_type = data.get("alert_name")
    paddle_event_id = data.get("alert_id")

    # Verify the webhook signature (implementation depends on Paddle library)

    # Log the payment event
    payment = Payment(
        paddle_event_id=paddle_event_id,
        event_type=event_type,
        amount=data.get("p_sale_gross"),
        currency=data.get("p_currency"),
        payload=data,
    )

    # Associate the payment with a user or team if possible
    user_id = data.get("passthrough", {}).get("user_id")
    team_id = data.get("passthrough", {}).get("team_id")
    if user_id:
        payment.user_id = user_id
    if team_id:
        payment.team_id = team_id

    db.session.add(payment)
    db.session.commit()

    # Handle specific event types
    if event_type == "subscription_payment_succeeded":
        # Add credits to the user or team
        trigger_payment_received_webhook(
            payment_id=payment.id,
            user_id=payment.user_id,
            team_id=payment.team_id,
            amount=payment.amount,
        )

    return jsonify({"status": "ok"}), HTTPStatus.OK
