"""API documentation configuration using Flask-RESTX."""
from __future__ import annotations

from flask import Flask
from flask_restx import Api, Resource, fields
from .schemas import CampaignSchema, ReferralSchema

# This would be used if we had Flask-RESTX installed
# For now, we'll create a simple documentation endpoint

def init_api_docs(app: Flask) -> None:
    """Initialize API documentation."""
    
    @app.route("/api/docs")
    def api_documentation():
        """Return API documentation in JSON format."""
        return {
            "info": {
                "title": "Cloud OTP API",
                "version": "1.0.0",
                "description": "Secure cloud-based OTP management platform with team collaboration, sharing, and enterprise features.",
                "contact": {
                    "name": "Cloud OTP Support",
                    "email": "<EMAIL>",
                    "url": "https://clotp.skpassegna.me/support"
                }
            },
            "servers": [
                {
                    "url": "/api",
                    "description": "Cloud OTP API Server"
                }
            ],
            "paths": {
                "/auth/register": {
                    "post": {
                        "summary": "Register a new user",
                        "tags": ["Authentication"],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["email", "password"],
                                        "properties": {
                                            "email": {"type": "string", "format": "email"},
                                            "password": {"type": "string", "minLength": 8},
                                            "secondary_password": {"type": "string", "minLength": 16},
                                            "encrypted_dek": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "User created successfully"},
                            "400": {"description": "Invalid input"},
                            "409": {"description": "Email already registered"}
                        }
                    }
                },
                "/auth/login": {
                    "post": {
                        "summary": "Authenticate user",
                        "tags": ["Authentication"],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["email", "password"],
                                        "properties": {
                                            "email": {"type": "string", "format": "email"},
                                            "password": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Authentication successful"},
                            "401": {"description": "Invalid credentials"}
                        }
                    }
                },
                "/auth/login/{provider}": {
                    "get": {
                        "summary": "Initiate OAuth login",
                        "tags": ["Authentication"],
                        "parameters": [
                            {
                                "name": "provider",
                                "in": "path",
                                "required": True,
                                "schema": {"type": "string", "enum": ["google", "github", "microsoft", "linkedin", "facebook"]}
                            }
                        ],
                        "responses": {
                            "302": {"description": "Redirect to OAuth provider"}
                        }
                    }
                },
                "/auth/providers": {
                    "get": {
                        "summary": "List available OAuth providers",
                        "tags": ["Authentication"],
                        "responses": {
                            "200": {
                                "description": "List of available providers",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "providers": {
                                                    "type": "array",
                                                    "items": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "/otp/": {
                    "get": {
                        "summary": "List OTPs",
                        "tags": ["OTP Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "folder_id", "in": "query", "schema": {"type": "integer"}},
                            {"name": "team_id", "in": "query", "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "List of OTPs"},
                            "401": {"description": "Authentication required"}
                        }
                    },
                    "post": {
                        "summary": "Create new OTP",
                        "tags": ["OTP Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["name", "secret_encrypted"],
                                        "properties": {
                                            "name": {"type": "string"},
                                            "secret_encrypted": {"type": "string"},
                                            "algorithm": {"type": "string", "default": "SHA1"},
                                            "digits": {"type": "integer", "default": 6},
                                            "period": {"type": "integer", "default": 30},
                                            "counter": {"type": "integer"},
                                            "metadata": {"type": "object"},
                                            "folder_id": {"type": "integer"},
                                            "team_id": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "OTP created successfully"},
                            "400": {"description": "Invalid input"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/otp/{otp_id}": {
                    "get": {
                        "summary": "Get OTP details",
                        "tags": ["OTP Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "otp_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "OTP details"},
                            "403": {"description": "Access denied"},
                            "404": {"description": "OTP not found"}
                        }
                    },
                    "put": {
                        "summary": "Update OTP",
                        "tags": ["OTP Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "otp_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "OTP updated successfully"},
                            "403": {"description": "Access denied"},
                            "404": {"description": "OTP not found"}
                        }
                    },
                    "delete": {
                        "summary": "Delete OTP",
                        "tags": ["OTP Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "otp_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "204": {"description": "OTP deleted successfully"},
                            "403": {"description": "Access denied"},
                            "404": {"description": "OTP not found"}
                        }
                    }
                },
                "/otp/{otp_id}/share": {
                    "post": {
                        "summary": "Share OTP",
                        "tags": ["OTP Sharing"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "otp_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["type"],
                                        "properties": {
                                            "type": {"type": "string", "enum": ["user", "team", "public"]},
                                            "email": {"type": "string", "format": "email"},
                                            "permission": {"type": "string", "enum": ["read", "write", "admin"]},
                                            "expires_at": {"type": "string", "format": "date-time"},
                                            "password_hash": {"type": "string"},
                                            "access_limit": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "OTP shared successfully"},
                            "403": {"description": "Access denied"},
                            "404": {"description": "User or OTP not found"}
                        }
                    }
                },
                "/otp/folders": {
                    "get": {
                        "summary": "List folders",
                        "tags": ["Folder Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "team_id", "in": "query", "schema": {"type": "integer"}},
                            {"name": "parent_id", "in": "query", "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "List of folders"}
                        }
                    },
                    "post": {
                        "summary": "Create folder",
                        "tags": ["Folder Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["name"],
                                        "properties": {
                                            "name": {"type": "string"},
                                            "description": {"type": "string"},
                                            "team_id": {"type": "integer"},
                                            "parent_id": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Folder created successfully"}
                        }
                    }
                },
                "/teams/": {
                    "get": {
                        "summary": "List user's teams",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "200": {"description": "List of teams"}
                        }
                    },
                    "post": {
                        "summary": "Create team",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["name"],
                                        "properties": {
                                            "name": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Team created successfully"}
                        }
                    }
                },
                "/teams/<int:team_id>/invite": {
                    "post": {
                        "summary": "Invite a user to a team by email",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["email"],
                                        "properties": {
                                            "email": {"type": "string", "format": "email"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Invitation created and email sent"},
                            "400": {"description": "Invalid input"},
                            "403": {"description": "Insufficient permissions"},
                            "409": {"description": "Invitation already sent"}
                        }
                    }
                },
                "/teams/<int:team_id>/invitations": {
                    "get": {
                        "summary": "List pending team invitations (paginated)",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "page", "in": "query", "schema": {"type": "integer"}},
                            {"name": "per_page", "in": "query", "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "Paginated list of invitations"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/teams/invitations/accept": {
                    "post": {
                        "summary": "Accept a team invitation by token",
                        "tags": ["Team Management"],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["token"],
                                        "properties": {
                                            "token": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Invitation accepted, user added to team"},
                            "400": {"description": "Invalid or expired token"},
                            "404": {"description": "User not found"}
                        }
                    }
                },
                "/teams/invitations/<int:invitation_id>": {
                    "delete": {
                        "summary": "Revoke/cancel a pending team invitation",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "204": {"description": "Invitation revoked"},
                            "400": {"description": "Only pending invitations can be revoked"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/teams/<int:team_id>/members/<int:user_id>": {
                    "patch": {
                        "summary": "Change a team member's role",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["role"],
                                        "properties": {
                                            "role": {"type": "string", "enum": ["admin", "member", "read-only"]}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Role updated"},
                            "400": {"description": "Invalid role or cannot change owner role"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/teams/<int:team_id>/members": {
                    "get": {
                        "summary": "List team members with roles (paginated)",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "page", "in": "query", "schema": {"type": "integer"}},
                            {"name": "per_page", "in": "query", "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "Paginated list of team members"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/teams/<int:team_id>/activity": {
                    "get": {
                        "summary": "List team activity logs (paginated, filterable)",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "page", "in": "query", "schema": {"type": "integer"}},
                            {"name": "per_page", "in": "query", "schema": {"type": "integer"}},
                            {"name": "action", "in": "query", "schema": {"type": "string"}},
                            {"name": "user_id", "in": "query", "schema": {"type": "integer"}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}}
                        ],
                        "responses": {
                            "200": {"description": "Paginated list of activity logs"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/teams/<int:team_id>/resources": {
                    "get": {
                        "summary": "View team resource allocations (paginated)",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "page", "in": "query", "schema": {"type": "integer"}},
                            {"name": "per_page", "in": "query", "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "Paginated list of resource allocations"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    },
                    "patch": {
                        "summary": "Update team resource allocations",
                        "tags": ["Team Management"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "resources": {
                                                "type": "array",
                                                "items": {
                                                    "type": "object",
                                                    "required": ["resource_type", "limit"],
                                                    "properties": {
                                                        "resource_type": {"type": "string"},
                                                        "limit": {"type": "integer"}
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Resource allocations updated"},
                            "403": {"description": "Insufficient permissions"}
                        }
                    }
                },
                "/keys/": {
                    "get": {
                        "summary": "List API keys",
                        "tags": ["API Keys"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "200": {"description": "List of API keys"}
                        }
                    },
                    "post": {
                        "summary": "Create API key",
                        "tags": ["API Keys"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "201": {"description": "API key created successfully"}
                        }
                    }
                },
                "/support/tickets": {
                    "get": {
                        "summary": "List support tickets",
                        "tags": ["Support"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "200": {"description": "List of support tickets"}
                        }
                    },
                    "post": {
                        "summary": "Create support ticket",
                        "tags": ["Support"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["subject"],
                                        "properties": {
                                            "subject": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Support ticket created successfully"}
                        }
                    }
                },
                "/secondary-password/set": {
                    "post": {
                        "summary": "Set secondary password",
                        "tags": ["Security"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["secondary_password", "encrypted_dek"],
                                        "properties": {
                                            "secondary_password": {"type": "string", "minLength": 16},
                                            "encrypted_dek": {"type": "string"},
                                            "current_secondary_password": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Secondary password set successfully"},
                            "400": {"description": "Invalid input"},
                            "401": {"description": "Invalid current password"}
                        }
                    }
                },
                "/secondary-password/verify": {
                    "post": {
                        "summary": "Verify secondary password",
                        "tags": ["Security"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["secondary_password"],
                                        "properties": {
                                            "secondary_password": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Password verified, returns encrypted DEK"},
                            "401": {"description": "Invalid password"},
                            "423": {"description": "Account locked due to failed attempts"}
                        }
                    }
                },
                "/webhooks/config": {
                    "post": {
                        "summary": "Create a webhook configuration",
                        "tags": ["Webhooks"],
                        "security": [{"bearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["target_url"],
                                        "properties": {
                                            "target_url": {"type": "string", "format": "uri"},
                                            "team_id": {"type": "integer"},
                                            "secret": {"type": "string"},
                                            "is_active": {"type": "boolean"},
                                            "subscribed_events": {"type": "string", "description": "e.g., 'otp.shared,user.registered'"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Webhook configuration created"},
                            "403": {"description": "Access denied"}
                        }
                    },
                    "get": {
                        "summary": "List webhook configurations",
                        "tags": ["Webhooks"],
                        "security": [{"bearerAuth": []}],
                        "responses": {
                            "200": {"description": "List of configurations"}
                        }
                    }
                },
                "/webhooks/config/{config_id}": {
                    "get": {
                        "summary": "Get a webhook configuration",
                        "tags": ["Webhooks"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [{"name": "config_id", "in": "path", "required": True, "schema": {"type": "integer"}}],
                        "responses": {
                            "200": {"description": "Configuration details"},
                            "403": {"description": "Access denied"}
                        }
                    },
                    "put": {
                        "summary": "Update a webhook configuration",
                        "tags": ["Webhooks"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [{"name": "config_id", "in": "path", "required": True, "schema": {"type": "integer"}}],
                        "requestBody": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "target_url": {"type": "string", "format": "uri"},
                                            "secret": {"type": "string"},
                                            "is_active": {"type": "boolean"},
                                            "subscribed_events": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Configuration updated"},
                            "403": {"description": "Access denied"}
                        }
                    },
                    "delete": {
                        "summary": "Delete a webhook configuration",
                        "tags": ["Webhooks"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [{"name": "config_id", "in": "path", "required": True, "schema": {"type": "integer"}}],
                        "responses": {
                            "204": {"description": "Configuration deleted"},
                            "403": {"description": "Access denied"}
                        }
                    }
                },
                "/sso/saml/metadata": {
                    "get": {
                        "summary": "Get SAML Service Provider metadata (XML)",
                        "tags": ["SSO/SAML"],
                        "responses": {
                            "200": {"description": "SAML SP metadata (XML)"}
                        }
                    }
                },
                "/sso/saml/login": {
                    "post": {
                        "summary": "Initiate SAML SSO login (redirect to IdP)",
                        "tags": ["SSO/SAML"],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "team_id": {"type": "integer", "description": "Team ID for SAML SSO"},
                                            "relay_state": {"type": "string", "description": "Optional relay state for post-login redirect"}
                                        },
                                        "required": ["team_id"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "302": {"description": "Redirect to SAML IdP login URL"},
                            "400": {"description": "Invalid request or SAML not configured"}
                        }
                    }
                },
                "/sso/saml/acs": {
                    "post": {
                        "summary": "SAML Assertion Consumer Service (ACS) endpoint",
                        "tags": ["SSO/SAML"],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/x-www-form-urlencoded": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "SAMLResponse": {"type": "string", "description": "Base64-encoded SAML assertion"},
                                            "RelayState": {"type": "string", "description": "Relay state from login initiation"}
                                        },
                                        "required": ["SAMLResponse"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "SAML assertion processed, user provisioned/logged in"},
                            "400": {"description": "Invalid SAML response or error"}
                        }
                    }
                },
                "/teams/{team_id}/saml-idp": {
                    "get": {
                        "summary": "Get SAML IdP config for a team",
                        "tags": ["SSO/SAML"],
                        "parameters": [
                            {"name": "team_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {"description": "SAML IdP config", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SAMLIdentityProvider"}}}},
                            "404": {"description": "Not found"}
                        }
                    },
                    "post": {
                        "summary": "Create SAML IdP config for a team",
                        "tags": ["SSO/SAML"],
                        "parameters": [
                            {"name": "team_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/SAMLIdentityProvider"}
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SAMLIdentityProvider"}}}},
                            "400": {"description": "Invalid input"}
                        }
                    },
                    "patch": {
                        "summary": "Update SAML IdP config for a team",
                        "tags": ["SSO/SAML"],
                        "parameters": [
                            {"name": "team_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/SAMLIdentityProvider"}
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SAMLIdentityProvider"}}}},
                            "400": {"description": "Invalid input"},
                            "404": {"description": "Not found"}
                        }
                    },
                    "delete": {
                        "summary": "Delete SAML IdP config for a team",
                        "tags": ["SSO/SAML"],
                        "parameters": [
                            {"name": "team_id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "204": {"description": "Deleted"},
                            "404": {"description": "Not found"}
                        }
                    }
                },
                "/teams/{team_id}/saml-sp-key": {
                    "post": {
                        "summary": "Upload SAML SP signing/encryption key and certificate (PEM)",
                        "tags": ["SSO/SAML"],
                        "security": [{"BearerAuth": []}],
                        "requestBody": {
                            "content": {
                                "multipart/form-data": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "key": {"type": "string", "format": "binary", "description": "SP private key (PEM)"},
                                            "cert": {"type": "string", "format": "binary", "description": "SP certificate (PEM)"}
                                        },
                                        "required": ["key", "cert"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "SP key/cert uploaded"},
                            "400": {"description": "Bad request"},
                            "403": {"description": "Forbidden"}
                        }
                    }
                },
                "/teams/{team_id}/saml-sp-key/generate": {
                    "post": {
                        "summary": "Generate SAML SP signing/encryption key and certificate (RSA 2048+)",
                        "tags": ["SSO/SAML"],
                        "security": [{"BearerAuth": []}],
                        "responses": {
                            "200": {"description": "SP key/cert generated"},
                            "403": {"description": "Forbidden"}
                        }
                    }
                },
                "/sso/saml/slo": {
                    "post": {
                        "summary": "Initiate SAML Single Logout (SLO)",
                        "tags": ["SSO/SAML"],
                        "requestBody": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "team_id": {"type": "integer", "description": "Team ID for SAML SLO"}
                                        },
                                        "required": ["team_id"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "302": {"description": "Redirect to IdP SLO URL"},
                            "400": {"description": "Bad request"},
                            "404": {"description": "No SLO URL configured"}
                        }
                    }
                },
                "/users/me/export-data": {
                    "get": {
                        "summary": "Export all personal data (GDPR/CCPA)",
                        "tags": ["Compliance"],
                        "security": [{"bearerAuth": []}],
                        "description": "Download all your personal data in machine-readable JSON format, including profile, OTPs, folders, notes, API keys, audit logs, tickets, payments, shares, webhook configs, and more.",
                        "responses": {
                            "200": {
                                "description": "All user data exported as JSON",
                                "content": {
                                    "application/json": {
                                        "schema": {"type": "object"}
                                    }
                                }
                            },
                            "401": {"description": "Authentication required"}
                        }
                    }
                },
                "/users/me/request-account-deletion": {
                    "post": {
                        "summary": "Request account deletion (GDPR/CCPA)",
                        "tags": ["Compliance"],
                        "security": [{"bearerAuth": []}],
                        "description": "Immediately and irreversibly deletes your account and all associated data. This action cannot be undone. All user data, including OTPs, folders, notes, API keys, audit logs, tickets, payments, shares, webhook configs, and more, will be permanently deleted.",
                        "responses": {
                            "200": {"description": "Account and all data deleted irreversibly"},
                            "401": {"description": "Authentication required"}
                        }
                    }
                },
                "/api/admin/campaigns": {
                    "post": {
                        "tags": ["Campaigns"],
                        "summary": "Create a new marketing or sales campaign.",
                        "description": "Create a new campaign. Only accessible to marketing/sales admins.",
                        "security": [{"BearerAuth": []}],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": CampaignSchema
                                }
                            }
                        },
                        "responses": {
                            "201": {
                                "description": "Campaign created successfully.",
                                "content": {"application/json": {"schema": CampaignSchema}}
                            },
                            "400": {"description": "Missing required fields."},
                            "409": {"description": "Campaign name already exists."}
                        }
                    }
                },
                "/api/admin/campaigns/{id}/performance": {
                    "get": {
                        "tags": ["Campaigns"],
                        "summary": "Get campaign performance analytics.",
                        "description": "Get signups, revenue, coupon usage, and referral analytics for a campaign. Only accessible to marketing/sales admins.",
                        "security": [{"BearerAuth": []}],
                        "parameters": [
                            {"name": "id", "in": "path", "required": True, "schema": {"type": "integer"}}
                        ],
                        "responses": {
                            "200": {
                                "description": "Campaign performance analytics.",
                                "content": {"application/json": {"schema": {"type": "object"}}}
                            },
                            "404": {"description": "Campaign not found."}
                        }
                    }
                },
                "/analytics/dashboard/overview": {
                    "get": {
                        "summary": "Get high-level overview statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "Overview statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/dashboard/user-growth": {
                    "get": {
                        "summary": "Get user growth statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "User growth statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/dashboard/api-usage": {
                    "get": {
                        "summary": "Get API usage statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "API usage statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/dashboard/security": {
                    "get": {
                        "summary": "Get security statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "Security statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/sales/overview": {
                    "get": {
                        "summary": "Get sales overview statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "Sales overview statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/marketing/overview": {
                    "get": {
                        "summary": "Get marketing overview statistics for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "Marketing overview statistics.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/reports/user-activity": {
                    "get": {
                        "summary": "Get user activity reports for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "User activity reports.", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/analytics/reports/system-health": {
                    "get": {
                        "summary": "Get system health and performance metrics",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "description": "Returns database, webhook, error, Celery, and Redis health metrics, including DB pool stats, Celery queue length, and Redis memory usage.",
                        "responses": {
                            "200": {
                                "description": "System health metrics",
                                "content": {"application/json": {"schema": {
                                    "type": "object",
                                    "properties": {
                                        "database": {"type": "object"},
                                        "webhooks": {"type": "object"},
                                        "errors": {"type": "object"},
                                        "celery": {"type": "object"},
                                        "redis": {"type": "object"},
                                        "overall_health": {"type": "string"},
                                    },
                                }}},
                            },
                            401: {"description": "Unauthorized"},
                            403: {"description": "Forbidden"},
                        }
                    }
                },
                "/analytics/export/users": {
                    "get": {
                        "summary": "Export user data for admin dashboard.",
                        "tags": ["Analytics"],
                        "security": [{"bearerAuth": []}],
                        "parameters": [
                            {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["today", "week", "month", "quarter", "year"]}},
                            {"name": "start_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "end_date", "in": "query", "schema": {"type": "string", "format": "date-time"}},
                            {"name": "user_segment", "in": "query", "schema": {"type": "string"}},
                        ],
                        "responses": {
                            "200": {"description": "User data exported as JSON", "content": {"application/json": {"schema": {"type": "object"}}}},
                            "401": {"description": "Authentication required"},
                            "403": {"description": "Admin access required"}
                        }
                    }
                },
                "/api/admin/developer/accounts": {
                    "get": {
                        "summary": "List all developer accounts",
                        "tags": ["Developer Admin"],
                        "description": "Returns all users with the 'Developer' or 'Developer Admin' role.",
                        "security": [{"BearerAuth": []}],
                        "responses": {
                            200: {
                                "description": "List of developer users",
                                "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}},
                            },
                            401: {"description": "Unauthorized"},
                            403: {"description": "Forbidden"},
                        },
                    }
                },
                "/api/admin/developer/resource-allocation": {
                    "get": {
                        "summary": "Get resource allocation for a user or team",
                        "tags": ["Developer Admin"],
                        "description": "Returns API quota, rate limit, storage limit, and current usage for a user or team.",
                        "parameters": [
                            {"name": "user_id", "in": "query", "required": False, "schema": {"type": "integer"}},
                            {"name": "team_id", "in": "query", "required": False, "schema": {"type": "integer"}},
                        ],
                        "security": [{"BearerAuth": []}],
                        "responses": {
                            200: {
                                "description": "Resource allocation and usage",
                                "content": {"application/json": {"schema": {
                                    "type": "object",
                                    "properties": {
                                        "resource_limits": {"type": "object"},
                                        "storage_used": {"type": "object"},
                                    },
                                }}},
                            },
                            400: {"description": "Missing user_id or team_id"},
                            401: {"description": "Unauthorized"},
                            403: {"description": "Forbidden"},
                            404: {"description": "User or Team not found"},
                        },
                    }
                },
                "/api/admin/developer/resource-allocation": {
                    "post": {
                        "summary": "Set resource allocation for a user or team",
                        "tags": ["Developer Admin"],
                        "description": "Set API quota, rate limit, and storage limit for a user or team. All changes are audit logged.",
                        "requestBody": {
                            "required": True,
                            "content": {"application/json": {"schema": {
                                "type": "object",
                                "properties": {
                                    "user_id": {"type": "integer"},
                                    "team_id": {"type": "integer"},
                                    "api_quota_per_month": {"type": "integer"},
                                    "api_rate_limit_per_second": {"type": "integer"},
                                    "storage_limit": {"type": "integer"},
                                },
                            }}}
                        },
                        "security": [{"BearerAuth": []}],
                        "responses": {
                            200: {"description": "Resources allocated successfully"},
                            400: {"description": "Missing user_id or team_id"},
                            401: {"description": "Unauthorized"},
                            403: {"description": "Forbidden"},
                            404: {"description": "User or Team not found"},
                        },
                    }
                },
                "/admin/notifications": {
                    "get": {
                        "summary": "List in-app notifications for admin",
                        "tags": ["Admin", "Notifications"],
                        "security": [{"bearerAuth": []}],
                        "description": "Returns a list of in-app notifications for the current admin (Super/Developer Admin). Supports optional filters: unread, severity.",
                        "parameters": [
                            {"name": "unread", "in": "query", "schema": {"type": "string"}, "description": "Filter for unread notifications (true/false)"},
                            {"name": "severity", "in": "query", "schema": {"type": "string"}, "description": "Filter by severity (info, warning, critical)"}
                        ],
                        "responses": {
                            "200": {
                                "description": "List of notifications",
                                "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}
                            }
                        }
                    }
                },
                "/admin/notifications/mark-read": {
                    "post": {
                        "summary": "Mark notifications as read",
                        "tags": ["Admin", "Notifications"],
                        "security": [{"bearerAuth": []}],
                        "description": "Mark one or more notifications as read by ID.",
                        "requestBody": {
                            "required": True,
                            "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}}}, "required": ["ids"]}}}
                        },
                        "responses": {"200": {"description": "Success"}}
                    }
                },
                "/admin/notifications/dismiss": {
                    "post": {
                        "summary": "Dismiss notifications",
                        "tags": ["Admin", "Notifications"],
                        "security": [{"bearerAuth": []}],
                        "description": "Dismiss one or more notifications by ID.",
                        "requestBody": {
                            "required": True,
                            "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}}}, "required": ["ids"]}}}
                        },
                        "responses": {"200": {"description": "Success"}}
                    }
                },
            },
            "components": {
                "securitySchemes": {
                    "bearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    },
                    "apiKeyAuth": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-API-Key"
                    }
                },
                "schemas": {
                    "User": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "email": {"type": "string", "format": "email"},
                            "first_name": {"type": "string"},
                            "last_name": {"type": "string"},
                            "is_active": {"type": "boolean"},
                            "credits": {"type": "integer"},
                            "created_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "OTP": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "algorithm": {"type": "string"},
                            "digits": {"type": "integer"},
                            "period": {"type": "integer"},
                            "counter": {"type": "integer"},
                            "metadata": {"type": "object"},
                            "folder_id": {"type": "integer"},
                            "created_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "Team": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "credits": {"type": "integer"},
                            "created_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "Folder": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "description": {"type": "string"},
                            "parent_id": {"type": "integer"},
                            "created_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "APIKey": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "prefix": {"type": "string"},
                            "expires_at": {"type": "string", "format": "date-time"},
                            "last_used_at": {"type": "string", "format": "date-time"},
                            "is_active": {"type": "boolean"},
                            "created_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "Error": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "string"},
                            "message": {"type": "string"},
                            "details": {"type": "object"}
                        }
                    },
                    "SAMLIdentityProvider": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "team_id": {"type": "integer"},
                            "idp_name": {"type": "string"},
                            "entity_id": {"type": "string"},
                            "sso_url": {"type": "string", "format": "uri"},
                            "x509_cert": {"type": "string"},
                            "slo_url": {"type": "string", "format": "uri"},
                            "x509_cert_signing": {"type": "string"},
                            "attribute_mapping": {"type": "object"},
                            "is_active": {"type": "boolean"},
                            "created_at": {"type": "string", "format": "date-time"},
                            "updated_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "Campaign": CampaignSchema,
                    "Referral": ReferralSchema,
                    "Notification": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "user_id": {"type": "integer", "nullable": True},
                            "team_id": {"type": "integer", "nullable": True},
                            "type": {"type": "string"},
                            "severity": {"type": "string"},
                            "message": {"type": "string"},
                            "metadata": {"type": "object"},
                            "is_read": {"type": "boolean"},
                            "is_dismissed": {"type": "boolean"},
                            "created_at": {"type": "string", "format": "date-time"},
                            "updated_at": {"type": "string", "format": "date-time"}
                        }
                    },
                    "SystemSetting": {
                        "type": "object",
                        "properties": {
                            "alert_threshold_failed_webhooks": {"type": "integer", "description": "Threshold for failed webhooks before alert is triggered (default: 50)"},
                            "alert_threshold_error_rate": {"type": "number", "format": "float", "description": "Threshold for system error rate (%) before alert is triggered (default: 5.0)"},
                            "alert_threshold_low_credits": {"type": "integer", "description": "Threshold for low credits on user accounts before alert is triggered (default: 10)"},
                        }
                    },
                }
            },
            "tags": [
                {"name": "Authentication", "description": "User authentication and OAuth"},
                {"name": "OTP Management", "description": "CRUD operations for OTP entries"},
                {"name": "OTP Sharing", "description": "Share OTPs with users and teams"},
                {"name": "Folder Management", "description": "Organize OTPs in folders"},
                {"name": "Team Management", "description": "Team creation and member management"},
                {"name": "API Keys", "description": "API key management for automation"},
                {"name": "Support", "description": "Support ticket system"},
                {"name": "Security", "description": "Security features and secondary password"},
                {"name": "Admin", "description": "Administrative functions"},
                {"name": "Analytics", "description": "Usage analytics and reporting"},
                {"name": "Webhooks", "description": "Webhook management and delivery"},
                {"name": "SSO/SAML", "description": "SAML SSO management"},
                {"name": "Compliance", "description": "Data compliance and account deletion"},
                {"name": "Campaigns", "description": "Marketing and sales campaign management and analytics."},
                {"name": "Developer Admin", "description": "Developer-specific administrative functions"},
                {"name": "Notifications", "description": "Notification management for admin users"}
            ]
        }
    
    @app.route("/api/docs/swagger")
    def swagger_ui():
        """Return Swagger UI HTML."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Cloud OTP API Documentation</title>
            <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
            <style>
                html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
                *, *:before, *:after { box-sizing: inherit; }
                body { margin:0; background: #fafafa; }
            </style>
        </head>
        <body>
            <div id="swagger-ui"></div>
            <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
            <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
            <script>
                window.onload = function() {
                    const ui = SwaggerUIBundle({
                        url: '/api/docs',
                        dom_id: '#swagger-ui',
                        deepLinking: true,
                        presets: [
                            SwaggerUIBundle.presets.apis,
                            SwaggerUIStandalonePreset
                        ],
                        plugins: [
                            SwaggerUIBundle.plugins.DownloadUrl
                        ],
                        layout: "StandaloneLayout"
                    });
                };
            </script>
        </body>
        </html>
        """