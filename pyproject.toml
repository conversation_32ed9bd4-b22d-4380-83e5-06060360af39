[project]
name = "cloud-otp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.16.2",
    "argon2-cffi>=25.1.0",
    "authlib>=1.6.0",
    "celery>=5.5.3",
    "cryptography>=45.0.5",
    "email-validator>=2.2.0",
    "flask>=3.1.1",
    "flask-bcrypt>=1.0.1",
    "flask-cors>=6.0.1",
    "flask-healthz>=1.0.1",
    "flask-jwt-extended>=4.7.1",
    "flask-limiter>=3.12",
    "flask-mail>=0.10.0",
    "flask-marshmallow>=1.3.0",
    "flask-migrate>=4.1.0",
    "flask-restx>=1.3.0",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "healthcheck>=1.3.3",
    "marshmallow-sqlalchemy>=1.4.2",
    "paddle-python-sdk>=1.7.0",
    "pandas>=2.3.0",
    "prometheus-client>=0.22.1",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.7",
    "pysaml2>=7.5.2",
    "pytest>=8.4.1",
    "python-dotenv>=1.1.1",
    "python-json-logger>=3.3.0",
    "redis>=6.2.0",
    "sentry-sdk>=2.32.0",
    "sqlalchemy-utils>=0.41.2",
]
