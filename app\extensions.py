"""Centralised initialisation of Flask extensions."""
from __future__ import annotations

import os
from flask_bcrypt import Bcrypt
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, get_jwt_identity
from flask_migrate import Migrate
from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from redis import Redis
from celery import Celery
from flask_mail import Mail
from flask_cors import CORS
from healthcheck import HealthCheck, EnvironmentDump
from flask_healthz import healthz, Healthz
from prometheus_client import Counter, Histogram
import logging
from pythonjsonlogger import jsonlogger

# Instantiate extensions (without app) ---------------------------------------
db: SQLAlchemy = SQLAlchemy()
ma: Marshmallow = Marshmallow()

migrate: Migrate = Migrate()

bcrypt: Bcrypt = Bcrypt()

jwt: JWTManager = JWTManager()

# Redis client for caching and sessions
redis_client: Redis = Redis.from_url(
    os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    decode_responses=True
)

# Celery for background tasks
celery: Celery = Celery("cloud_otp")

# Flask-Mail for email
mail = Mail()

# Flask-CORS for CORS support
cors = CORS()

# HealthCheck and EnvironmentDump for /healthcheck and /environment
health = HealthCheck()
envdump = EnvironmentDump()

# Prometheus metrics
api_requests_total = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint'])
api_request_duration = Histogram('api_request_duration_seconds', 'API request duration')

# flask-healthz (Kubernetes liveness/readiness)
healthz_ext = Healthz

# Flask-Limiter for production-grade rate limiting
def get_user_id_for_limiter():
    """Get user ID for rate limiting, fallback to IP address."""
    try:
        user_id = get_jwt_identity()
        return f"user:{user_id}" if user_id else get_remote_address()
    except:
        return get_remote_address()

limiter = Limiter(
    key_func=get_user_id_for_limiter,
    storage_uri=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    default_limits=["2000 per day", "100 per hour"],
    headers_enabled=True,
    retry_after="http-date"
)

# Structured JSON logger for production
def setup_json_logging():
    """Setup structured JSON logging for production environments."""
    json_handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        '%(asctime)s %(name)s %(levelname)s %(message)s %(pathname)s %(lineno)d'
    )
    json_handler.setFormatter(formatter)
    return json_handler
