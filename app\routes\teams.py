"""Endpoints for managing teams and team members."""
from __future__ import annotations

from http import HTTPStatus

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db, mail
from ..models import Team, User, TeamMember, Invitation, AuditLog, TeamResourceAllocation
from ..schemas import TeamSchema, TeamMemberSchema, InvitationSchema, AuditLogSchema, TeamResourceAllocationSchema
from flask_mail import Message
import secrets
from datetime import datetime, timedelta
from ..tasks import send_email
from ..webhooks import trigger_team_member_added_webhook

team_bp = Blueprint("teams", __name__)

team_schema = TeamSchema()
teams_schema = TeamSchema(many=True)
team_member_schema = TeamMemberSchema()
invitation_schema = InvitationSchema()
invitation_list_schema = InvitationSchema(many=True)
audit_log_schema = AuditLogSchema()
audit_log_list_schema = AuditLogSchema(many=True)
resource_allocation_schema = TeamResourceAllocationSchema()
resource_allocation_list_schema = TeamResourceAllocationSchema(many=True)


@team_bp.route("/", methods=["POST"])
@jwt_required()
def create_team():
    """Create a new team."""
    user_id = get_jwt_identity()
    current_user = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    team = Team(name=data.get("name"), owner=current_user)
    db.session.add(team)
    db.session.commit()
    return team_schema.jsonify(team), HTTPStatus.CREATED


@team_bp.route("/<int:team_id>", methods=["GET"])
@jwt_required()
def get_team(team_id: int):
    """Get details for a specific team."""
    team = Team.query.get_or_404(team_id)
    return team_schema.jsonify(team)


@team_bp.route("/<int:team_id>/members", methods=["POST"])
@jwt_required()
def invite_member(team_id: int):
    """Invite a user to a team."""
    team = Team.query.get_or_404(team_id)
    user_id = get_jwt_identity()
    if team.owner_id != user_id:
        return jsonify({"msg": "Only team owners can invite members"}), HTTPStatus.FORBIDDEN

    data = request.get_json() or {}
    invited_user_email = data.get("email")
    invited_user = User.query.filter_by(email=invited_user_email).first()
    if not invited_user:
        return jsonify({"msg": f"User with email '{invited_user_email}' not found"}), HTTPStatus.NOT_FOUND

    team_member = TeamMember(team=team, user=invited_user, role=data.get("role", "member"))
    db.session.add(team_member)
    db.session.commit()
    trigger_team_member_added_webhook(
        team_id=team_member.team_id,
        user_id=team_member.user_id,
        role=team_member.role,
    )
    return team_member_schema.jsonify(team_member), HTTPStatus.CREATED


@team_bp.route("/<int:team_id>/members/<int:member_id>", methods=["DELETE"])
@jwt_required()
def remove_member(team_id: int, member_id: int):
    """Remove a user from a team."""
    team = Team.query.get_or_404(team_id)
    user_id = get_jwt_identity()
    if team.owner_id != user_id:
        return jsonify({"msg": "Only team owners can remove members"}), HTTPStatus.FORBIDDEN

    team_member = TeamMember.query.filter_by(team_id=team_id, id=member_id).first_or_404()
    db.session.delete(team_member)
    db.session.commit()
    return "", HTTPStatus.NO_CONTENT


# Helper: permission check for team admin/owner
def is_team_admin(user_id, team):
    if team.owner_id == user_id:
        return True
    member = TeamMember.query.filter_by(team_id=team.id, user_id=user_id).first()
    return member and member.role == "admin"


# POST /teams/<team_id>/invite
@team_bp.route("/<int:team_id>/invite", methods=["POST"])
@jwt_required()
def invite_team_member(team_id):
    user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(user_id, team):
        return jsonify({"msg": "Only team admins/owners can invite members"}), HTTPStatus.FORBIDDEN
    data = request.get_json() or {}
    email = data.get("email")
    if not email:
        return jsonify({"msg": "Email is required"}), HTTPStatus.BAD_REQUEST
    # Check for existing invitation
    existing = Invitation.query.filter_by(team_id=team_id, email=email, status="pending").first()
    if existing:
        return jsonify({"msg": "Invitation already sent to this email"}), HTTPStatus.CONFLICT
    # Generate token and expiry
    token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(days=3)
    invitation = Invitation(
        code=secrets.token_urlsafe(8),
        created_by_id=user_id,
        team_id=team_id,
        email=email,
        token=token,
        status="pending",
        expires_at=expires_at,
        is_active=True,
    )
    db.session.add(invitation)
    db.session.commit()
    # Send invitation email
    message_data = {
        "subject": f"You're invited to join team '{team.name}' on Cloud OTP",
        "recipients": [email],
        "body": f"You have been invited to join the team '{team.name}'. Click the link below to accept:\n\nhttps://yourdomain.com/accept-invite?token={token}\n\nThis invitation expires in 3 days."
    }
    send_email.delay(message_data)
    return invitation_schema.jsonify(invitation), HTTPStatus.CREATED


# GET /teams/<int:team_id>/invitations
@team_bp.route("/<int:team_id>/invitations", methods=["GET"])
@jwt_required()
def list_team_invitations(team_id):
    user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(user_id, team):
        return jsonify({"msg": "Only team admins/owners can view invitations"}), HTTPStatus.FORBIDDEN
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    q = Invitation.query.filter_by(team_id=team_id, status="pending").order_by(Invitation.created_at.desc())
    pagination = q.paginate(page=page, per_page=per_page, error_out=False)
    items = invitation_list_schema.dump(pagination.items)
    return jsonify({
        "total": pagination.total,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "pages": pagination.pages,
        "items": items
    })


# POST /teams/invitations/accept
@team_bp.route("/invitations/accept", methods=["POST"])
def accept_team_invitation():
    data = request.get_json() or {}
    token = data.get("token")
    if not token:
        return jsonify({"msg": "Token is required"}), HTTPStatus.BAD_REQUEST
    invitation = Invitation.query.filter_by(token=token, status="pending", is_active=True).first()
    if not invitation or (invitation.expires_at and invitation.expires_at < datetime.utcnow()):
        return jsonify({"msg": "Invalid or expired invitation"}), HTTPStatus.BAD_REQUEST
    # Find or create user by email
    user = User.query.filter_by(email=invitation.email).first()
    if not user:
        return jsonify({"msg": "No user with this email. Please sign up first."}), HTTPStatus.NOT_FOUND
    # Add to team if not already a member
    existing = TeamMember.query.filter_by(team_id=invitation.team_id, user_id=user.id).first()
    if not existing:
        team_member = TeamMember(team_id=invitation.team_id, user_id=user.id, role="member")
        db.session.add(team_member)
    # Mark invitation as accepted
    invitation.status = "accepted"
    invitation.is_active = False
    db.session.commit()
    return jsonify({"msg": "Invitation accepted. You are now a team member."})


# DELETE /teams/invitations/<invitation_id>
@team_bp.route("/invitations/<int:invitation_id>", methods=["DELETE"])
@jwt_required()
def revoke_team_invitation(invitation_id):
    user_id = get_jwt_identity()
    invitation = Invitation.query.get_or_404(invitation_id)
    team = Team.query.get(invitation.team_id)
    if not team or not is_team_admin(user_id, team):
        return jsonify({"msg": "Only team admins/owners can revoke invitations"}), HTTPStatus.FORBIDDEN
    if invitation.status != "pending":
        return jsonify({"msg": "Only pending invitations can be revoked"}), HTTPStatus.BAD_REQUEST
    invitation.status = "revoked"
    invitation.is_active = False
    db.session.commit()
    return "", HTTPStatus.NO_CONTENT


# PATCH /teams/<int:team_id>/members/<int:user_id>
@team_bp.route("/<int:team_id>/members/<int:user_id>", methods=["PATCH"])
@jwt_required()
def change_member_role(team_id, user_id):
    current_user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(current_user_id, team):
        return jsonify({"msg": "Only team admins/owners can change member roles"}), HTTPStatus.FORBIDDEN
    if user_id == team.owner_id:
        return jsonify({"msg": "Cannot change role of team owner"}), HTTPStatus.BAD_REQUEST
    data = request.get_json() or {}
    new_role = data.get("role")
    if new_role not in ["admin", "member", "read-only"]:
        return jsonify({"msg": "Invalid role"}), HTTPStatus.BAD_REQUEST
    member = TeamMember.query.filter_by(team_id=team_id, user_id=user_id).first_or_404()
    member.role = new_role
    db.session.commit()
    return team_member_schema.jsonify(member)


# GET /teams/<int:team_id>/members
@team_bp.route("/<int:team_id>/members", methods=["GET"])
@jwt_required()
def list_team_members(team_id):
    current_user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(current_user_id, team):
        return jsonify({"msg": "Only team admins/owners can view members"}), HTTPStatus.FORBIDDEN
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    q = TeamMember.query.filter_by(team_id=team_id).order_by(TeamMember.created_at.desc())
    pagination = q.paginate(page=page, per_page=per_page, error_out=False)
    items = [team_member_schema.dump(m) for m in pagination.items]
    return jsonify({
        "total": pagination.total,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "pages": pagination.pages,
        "items": items
    })


# GET /teams/<int:team_id>/activity
@team_bp.route("/<int:team_id>/activity", methods=["GET"])
@jwt_required()
def list_team_activity(team_id):
    current_user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    member = TeamMember.query.filter_by(team_id=team_id, user_id=current_user_id).first()
    is_admin = is_team_admin(current_user_id, team)
    # Only admins/owners can view all logs; members can view their own actions
    if not (is_admin or member):
        return jsonify({"msg": "Not authorized for this team"}), HTTPStatus.FORBIDDEN
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    action = request.args.get("action")
    user_id = request.args.get("user_id")
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    q = AuditLog.query.filter_by(team_id=team_id)
    if not is_admin:
        q = q.filter_by(user_id=current_user_id)
    if action:
        q = q.filter(AuditLog.action == action)
    if user_id:
        q = q.filter(AuditLog.user_id == int(user_id))
    if start_date:
        q = q.filter(AuditLog.created_at >= start_date)
    if end_date:
        q = q.filter(AuditLog.created_at <= end_date)
    q = q.order_by(AuditLog.created_at.desc())
    pagination = q.paginate(page=page, per_page=per_page, error_out=False)
    items = audit_log_list_schema.dump(pagination.items)
    return jsonify({
        "total": pagination.total,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "pages": pagination.pages,
        "items": items
    })


# GET /teams/<int:team_id>/resources
@team_bp.route("/<int:team_id>/resources", methods=["GET"])
@jwt_required()
def get_team_resources(team_id):
    current_user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(current_user_id, team):
        return jsonify({"msg": "Only team admins/owners can view resource allocations"}), HTTPStatus.FORBIDDEN
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    q = TeamResourceAllocation.query.filter_by(team_id=team_id).order_by(TeamResourceAllocation.resource_type)
    pagination = q.paginate(page=page, per_page=per_page, error_out=False)
    items = resource_allocation_list_schema.dump(pagination.items)
    return jsonify({
        "total": pagination.total,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "pages": pagination.pages,
        "items": items
    })


# PATCH /teams/<int:team_id>/resources
@team_bp.route("/<int:team_id>/resources", methods=["PATCH"])
@jwt_required()
def update_team_resources(team_id):
    current_user_id = get_jwt_identity()
    team = Team.query.get_or_404(team_id)
    if not is_team_admin(current_user_id, team):
        return jsonify({"msg": "Only team admins/owners can update resource allocations"}), HTTPStatus.FORBIDDEN
    data = request.get_json() or {}
    updates = data.get("resources", [])
    updated = []
    for item in updates:
        resource_type = item.get("resource_type")
        limit = item.get("limit")
        if not resource_type or limit is None:
            continue
        allocation = TeamResourceAllocation.query.filter_by(team_id=team_id, resource_type=resource_type).first()
        if allocation:
            allocation.limit = limit
        else:
            allocation = TeamResourceAllocation(team_id=team_id, resource_type=resource_type, limit=limit, used=0)
            db.session.add(allocation)
        updated.append(resource_allocation_schema.dump(allocation))
    db.session.commit()
    return jsonify({"updated": updated})
