"""Coupon management endpoints."""
from __future__ import annotations

from http import HTTPStatus
from datetime import datetime, timedelta
import secrets

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import Coupon, User, Team
from ..schemas import CouponSchema
from ..routes.admin_helpers import admin_required, sales_required, audit_log

coupons_bp = Blueprint("coupons", __name__)

coupon_schema = CouponSchema()
coupons_schema = CouponSchema(many=True)


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


@coupons_bp.route("/", methods=["POST"])
@jwt_required()
@sales_required
@audit_log("Created coupon")
def create_coupon():
    """Create a new coupon code."""
    data = request.get_json() or {}
    
    # Validate required fields
    if not data.get("code") or not data.get("discount_type") or not data.get("value"):
        return jsonify({"msg": "Code, discount_type, and value are required"}), HTTPStatus.BAD_REQUEST
    
    # Validate discount type
    valid_discount_types = ["percentage", "fixed_amount", "credits"]
    if data["discount_type"] not in valid_discount_types:
        return jsonify({"msg": f"Invalid discount_type. Must be one of: {valid_discount_types}"}), HTTPStatus.BAD_REQUEST
    
    # Check if coupon code already exists
    existing_coupon = Coupon.query.filter_by(code=data["code"]).first()
    if existing_coupon:
        return jsonify({"msg": "Coupon code already exists"}), HTTPStatus.CONFLICT
    
    # Set expiration if provided
    expires_at = None
    if data.get("expires_in_days"):
        expires_at = datetime.utcnow() + timedelta(days=data["expires_in_days"])
    elif data.get("expires_at"):
        expires_at = datetime.fromisoformat(data["expires_at"])
    
    coupon = Coupon(
        code=data["code"],
        discount_type=data["discount_type"],
        value=data["value"],
        expires_at=expires_at,
        max_uses=data.get("max_uses"),
        is_active=data.get("is_active", True)
    )
    
    db.session.add(coupon)
    db.session.commit()
    
    return coupon_schema.jsonify(coupon), HTTPStatus.CREATED


@coupons_bp.route("/", methods=["GET"])
@jwt_required()
@sales_required
def list_coupons():
    """List all coupons (sales/admin only)."""
    active_only = request.args.get("active_only", "false").lower() == "true"
    
    query = Coupon.query
    if active_only:
        query = query.filter_by(is_active=True)
    
    coupons = query.all()
    return coupons_schema.jsonify(coupons)


@coupons_bp.route("/<int:coupon_id>", methods=["GET"])
@jwt_required()
@sales_required
def get_coupon(coupon_id: int):
    """Get details of a specific coupon."""
    coupon = Coupon.query.get_or_404(coupon_id)
    return coupon_schema.jsonify(coupon)


@coupons_bp.route("/<int:coupon_id>", methods=["PUT"])
@jwt_required()
@sales_required
@audit_log("Updated coupon")
def update_coupon(coupon_id: int):
    """Update a coupon."""
    coupon = Coupon.query.get_or_404(coupon_id)
    data = request.get_json() or {}
    
    # Update allowed fields
    if "discount_type" in data:
        valid_discount_types = ["percentage", "fixed_amount", "credits"]
        if data["discount_type"] not in valid_discount_types:
            return jsonify({"msg": f"Invalid discount_type. Must be one of: {valid_discount_types}"}), HTTPStatus.BAD_REQUEST
        coupon.discount_type = data["discount_type"]
    
    if "value" in data:
        coupon.value = data["value"]
    
    if "expires_at" in data:
        if data["expires_at"]:
            coupon.expires_at = datetime.fromisoformat(data["expires_at"])
        else:
            coupon.expires_at = None
    
    if "max_uses" in data:
        coupon.max_uses = data["max_uses"]
    
    if "is_active" in data:
        coupon.is_active = data["is_active"]
    
    db.session.commit()
    return coupon_schema.jsonify(coupon)


@coupons_bp.route("/<int:coupon_id>", methods=["DELETE"])
@jwt_required()
@sales_required
@audit_log("Deleted coupon")
def delete_coupon(coupon_id: int):
    """Delete a coupon."""
    coupon = Coupon.query.get_or_404(coupon_id)
    db.session.delete(coupon)
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


@coupons_bp.route("/validate/<string:code>", methods=["POST"])
@jwt_required()
def validate_coupon(code: str):
    """Validate a coupon code and return its details."""
    current_user = get_current_user()
    coupon = Coupon.query.filter_by(code=code, is_active=True).first()
    
    if not coupon:
        return jsonify({"msg": "Invalid coupon code"}), HTTPStatus.NOT_FOUND
    
    # Check if coupon has expired
    if coupon.expires_at and coupon.expires_at < datetime.utcnow():
        return jsonify({"msg": "Coupon has expired"}), HTTPStatus.GONE
    
    # Check if coupon has reached max uses
    if coupon.max_uses and coupon.use_count >= coupon.max_uses:
        return jsonify({"msg": "Coupon has reached maximum usage limit"}), HTTPStatus.GONE
    
    # Return coupon details for frontend to apply discount
    return jsonify({
        "valid": True,
        "discount_type": coupon.discount_type,
        "value": coupon.value,
        "description": f"{coupon.value}{'%' if coupon.discount_type == 'percentage' else ' credits' if coupon.discount_type == 'credits' else ' cents'} off"
    })


@coupons_bp.route("/apply/<string:code>", methods=["POST"])
@jwt_required()
@audit_log("Applied coupon")
def apply_coupon(code: str):
    """Apply a coupon code to user's account."""
    current_user = get_current_user()
    coupon = Coupon.query.filter_by(code=code, is_active=True).first()
    
    if not coupon:
        return jsonify({"msg": "Invalid coupon code"}), HTTPStatus.NOT_FOUND
    
    # Check if coupon has expired
    if coupon.expires_at and coupon.expires_at < datetime.utcnow():
        return jsonify({"msg": "Coupon has expired"}), HTTPStatus.GONE
    
    # Check if coupon has reached max uses
    if coupon.max_uses and coupon.use_count >= coupon.max_uses:
        return jsonify({"msg": "Coupon has reached maximum usage limit"}), HTTPStatus.GONE
    
    data = request.get_json() or {}
    target_type = data.get("target_type", "user")  # "user" or "team"
    target_id = data.get("target_id")
    
    # Apply coupon based on type
    if coupon.discount_type == "credits":
        if target_type == "user":
            current_user.credits += coupon.value
        elif target_type == "team" and target_id:
            team = Team.query.get_or_404(target_id)
            # Check if user has permission to apply coupon to team
            if team.owner_id != current_user.id:
                return jsonify({"msg": "Only team owners can apply coupons to teams"}), HTTPStatus.FORBIDDEN
            team.credits += coupon.value
        
        # Increment usage count
        coupon.use_count += 1
        db.session.commit()
        
        return jsonify({
            "msg": f"Successfully applied {coupon.value} credits",
            "credits_added": coupon.value
        })
    
    else:
        # For percentage and fixed_amount discounts, return discount info for payment processing
        return jsonify({
            "msg": "Coupon validated for payment processing",
            "discount_type": coupon.discount_type,
            "value": coupon.value,
            "coupon_id": coupon.id
        })


@coupons_bp.route("/generate", methods=["POST"])
@jwt_required()
@sales_required
@audit_log("Generated bulk coupons")
def generate_bulk_coupons():
    """Generate multiple coupon codes at once."""
    data = request.get_json() or {}
    
    count = data.get("count", 1)
    if count > 100:  # Limit bulk generation
        return jsonify({"msg": "Cannot generate more than 100 coupons at once"}), HTTPStatus.BAD_REQUEST
    
    prefix = data.get("prefix", "BULK")
    discount_type = data.get("discount_type", "credits")
    value = data.get("value", 100)
    expires_in_days = data.get("expires_in_days", 30)
    max_uses = data.get("max_uses", 1)
    
    expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
    
    generated_coupons = []
    for i in range(count):
        code = f"{prefix}_{secrets.token_urlsafe(8).upper()}"
        
        # Ensure uniqueness
        while Coupon.query.filter_by(code=code).first():
            code = f"{prefix}_{secrets.token_urlsafe(8).upper()}"
        
        coupon = Coupon(
            code=code,
            discount_type=discount_type,
            value=value,
            expires_at=expires_at,
            max_uses=max_uses,
            is_active=True
        )
        
        db.session.add(coupon)
        generated_coupons.append(code)
    
    db.session.commit()
    
    return jsonify({
        "msg": f"Generated {count} coupons",
        "coupons": generated_coupons
    }), HTTPStatus.CREATED


# Admin endpoints for coupon analytics
@coupons_bp.route("/admin/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_coupon_stats():
    """Get coupon usage statistics for admin dashboard."""
    total_coupons = Coupon.query.count()
    active_coupons = Coupon.query.filter_by(is_active=True).count()
    used_coupons = Coupon.query.filter(Coupon.use_count > 0).count()
    total_usage = db.session.query(db.func.sum(Coupon.use_count)).scalar() or 0
    
    # Credits distributed via coupons
    credits_distributed = db.session.query(
        db.func.sum(Coupon.value * Coupon.use_count)
    ).filter(Coupon.discount_type == "credits").scalar() or 0
    
    return jsonify({
        "total_coupons": total_coupons,
        "active_coupons": active_coupons,
        "used_coupons": used_coupons,
        "total_usage": total_usage,
        "credits_distributed": credits_distributed
    })