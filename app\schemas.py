"""Marshmallow schemas for serialising/deserialising models."""
from __future__ import annotations

from marshmallow import Schema, fields, post_load

from .models import (
    OTP,
    User,
    APIKey,
    Coupon,
    Invitation,
    AuditLog,
    Team,
    TeamMember,
    SystemSetting,
    Ticket,
    TicketMessage,
    Payment,
    Folder,
    OTPShare,
    Note,
    SecondaryPasswordAttempt,
    WebhookEvent,
    TeamResourceAllocation,
    WebhookConfig,
    FolderShare,
    SAMLIdentityProvider,
    Notification,
)


class RoleSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str()
    description = fields.Str()


class UserSchema(Schema):
    id = fields.Int(dump_only=True)
    email = fields.Email(required=True)
    first_name = fields.Str()
    last_name = fields.Str()
    phone_number = fields.Str()
    is_active = fields.Bool()
    credits = fields.Int()
    api_quota_per_month = fields.Int(allow_none=True)
    api_rate_limit_per_second = fields.Int(allow_none=True)
    roles = fields.Nested(RoleSchema, many=True, dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    referred_by_id = fields.Int(allow_none=True)
    campaign_id = fields.Int(allow_none=True)
    storage_limit = fields.Int(dump_only=True)


class TeamMemberSchema(Schema):
    id = fields.Int(dump_only=True)
    user = fields.Nested(UserSchema, dump_only=True)
    role = fields.Str()


class TeamSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    owner = fields.Nested(UserSchema, dump_only=True)
    members = fields.Nested(TeamMemberSchema, many=True, dump_only=True)
    credits = fields.Int()
    api_quota_per_month = fields.Int(allow_none=True)
    api_rate_limit_per_second = fields.Int(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    storage_limit = fields.Int(dump_only=True)


class OTPSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    secret_encrypted = fields.Str(required=True)
    algorithm = fields.Str()
    digits = fields.Int()
    period = fields.Int()
    counter = fields.Int(allow_none=True)
    metadata = fields.Dict()
    folder_id = fields.Int(allow_none=True)
    owner_id = fields.Int(dump_only=True, allow_none=True)
    team_id = fields.Int(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def _make_otp(self, data, **kwargs):  # noqa: D401
        return OTP(**data)


class APIKeySchema(Schema):
    id = fields.Int(dump_only=True)
    prefix = fields.Str(dump_only=True)
    expires_at = fields.DateTime(allow_none=True)
    last_used_at = fields.DateTime(dump_only=True, allow_none=True)
    is_active = fields.Bool()
    user_id = fields.Int(dump_only=True, allow_none=True)
    team_id = fields.Int(dump_only=True, allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class CouponSchema(Schema):
    id = fields.Int(dump_only=True)
    code = fields.Str(required=True)
    discount_type = fields.Str(required=True)
    value = fields.Int(required=True)
    expires_at = fields.DateTime(allow_none=True)
    max_uses = fields.Int(allow_none=True)
    use_count = fields.Int(dump_only=True)
    is_active = fields.Bool()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    campaign_id = fields.Int(allow_none=True)


class InvitationSchema(Schema):
    id = fields.Int(dump_only=True)
    code = fields.Str(required=True)
    created_by_id = fields.Int(required=True)
    team_id = fields.Int(allow_none=True)
    email = fields.Email(allow_none=True)
    token = fields.Str(allow_none=True)
    status = fields.Str()
    expires_at = fields.DateTime(allow_none=True)
    is_active = fields.Bool()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    campaign_id = fields.Int(allow_none=True)


class AuditLogSchema(Schema):
    id = fields.Int(dump_only=True)
    user_id = fields.Int(allow_none=True)
    team_id = fields.Int(allow_none=True)
    action = fields.Str()
    details = fields.Dict()
    created_at = fields.DateTime(dump_only=True)


class SystemSettingSchema(Schema):
    key = fields.Str()
    value = fields.Raw()


class TicketMessageSchema(Schema):
    id = fields.Int(dump_only=True)
    user = fields.Nested(UserSchema, dump_only=True)
    message = fields.Str()
    created_at = fields.DateTime(dump_only=True)


class TicketSchema(Schema):
    id = fields.Int(dump_only=True)
    subject = fields.Str(required=True)
    status = fields.Str()
    user = fields.Nested(UserSchema, dump_only=True)
    messages = fields.Nested(TicketMessageSchema, many=True, dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class PaymentSchema(Schema):
    id = fields.Int(dump_only=True)
    user_id = fields.Int(allow_none=True)
    team_id = fields.Int(allow_none=True)
    paddle_event_id = fields.Str()
    event_type = fields.Str()
    amount = fields.Int()
    currency = fields.Str()
    payload = fields.Dict()
    created_at = fields.DateTime(dump_only=True)


class FolderSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    owner_id = fields.Int(dump_only=True, allow_none=True)
    team_id = fields.Int(allow_none=True)
    parent_id = fields.Int(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def _make_folder(self, data, **kwargs):
        return Folder(**data)


class OTPShareSchema(Schema):
    id = fields.Int(dump_only=True)
    otp_id = fields.Int(required=True)
    shared_by_user_id = fields.Int(dump_only=True)
    shared_with_user_id = fields.Int(allow_none=True)
    shared_with_team_id = fields.Int(allow_none=True)
    share_type = fields.Str()
    permission = fields.Str()
    expires_at = fields.DateTime(allow_none=True)
    access_limit = fields.Int(allow_none=True)
    access_count = fields.Int(dump_only=True)
    is_active = fields.Bool()
    share_token = fields.Str(dump_only=True, allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def _make_otp_share(self, data, **kwargs):
        return OTPShare(**data)


class NoteSchema(Schema):
    id = fields.Int(dump_only=True)
    otp_id = fields.Int(required=True)
    author_id = fields.Int(dump_only=True)
    title = fields.Str()
    content_encrypted = fields.Str(required=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def _make_note(self, data, **kwargs):
        return Note(**data)


class SecondaryPasswordAttemptSchema(Schema):
    id = fields.Int(dump_only=True)
    user_id = fields.Int(required=True)
    ip_address = fields.Str()
    user_agent = fields.Str(allow_none=True)
    success = fields.Bool()
    created_at = fields.DateTime(dump_only=True)


class WebhookEventSchema(Schema):
    id = fields.Int(dump_only=True)
    event_type = fields.Str(required=True)
    payload = fields.Dict(required=True)
    target_url = fields.Str(required=True)
    status = fields.Str()
    attempts = fields.Int()
    last_attempt_at = fields.DateTime(allow_none=True)
    response_status = fields.Int(allow_none=True)
    response_body = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class TeamResourceAllocationSchema(Schema):
    id = fields.Int(dump_only=True)
    team_id = fields.Int(required=True)
    resource_type = fields.Str(required=True)
    limit = fields.Int(required=True)
    used = fields.Int()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class WebhookConfigSchema(Schema):
    id = fields.Int(dump_only=True)
    user_id = fields.Int(dump_only=True, allow_none=True)
    team_id = fields.Int(allow_none=True, load_only=True)
    target_url = fields.URL(required=True)
    secret = fields.Str(load_only=True, allow_none=True, description="A secret to sign webhook payloads. Not returned on read.")
    is_active = fields.Bool(default=True)
    subscribed_events = fields.Str(description="Comma-separated list of event types to subscribe to. '*' for all.", default="*")
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_webhook_config(self, data, **kwargs):
        return WebhookConfig(**data)


class FolderShareSchema(Schema):
    id = fields.Int(dump_only=True)
    folder_id = fields.Int(required=True)
    shared_by_user_id = fields.Int(dump_only=True)
    shared_with_user_id = fields.Int(allow_none=True)
    shared_with_team_id = fields.Int(allow_none=True)
    share_type = fields.Str()
    permission = fields.Str()
    expires_at = fields.DateTime(allow_none=True)
    access_limit = fields.Int(allow_none=True)
    access_count = fields.Int(dump_only=True)
    is_active = fields.Bool()
    share_token = fields.Str(dump_only=True, allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def _make_folder_share(self, data, **kwargs):
        return FolderShare(**data)


class SAMLIdentityProviderSchema(Schema):
    id = fields.Int(dump_only=True)
    team_id = fields.Int(required=True)
    idp_name = fields.Str(required=True)
    entity_id = fields.Str(required=True)
    sso_url = fields.URL(required=True)
    x509_cert = fields.Str(required=True, load_only=True)
    slo_url = fields.URL(allow_none=True)
    x509_cert_signing = fields.Str(allow_none=True, load_only=True)
    attribute_mapping = fields.Dict()
    is_active = fields.Bool()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_saml_idp(self, data, **kwargs):
        return SAMLIdentityProvider(**data)


class CampaignSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    type = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    start_date = fields.DateTime(allow_none=True)
    end_date = fields.DateTime(allow_none=True)
    is_active = fields.Bool()
    metadata = fields.Dict()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class ReferralSchema(Schema):
    id = fields.Int(dump_only=True)
    referred_user_id = fields.Int(required=True)
    referrer_user_id = fields.Int(allow_none=True)
    campaign_id = fields.Int(allow_none=True)
    invitation_id = fields.Int(allow_none=True)
    coupon_id = fields.Int(allow_none=True)
    method = fields.Str(allow_none=True)
    metadata = fields.Dict()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class NotificationSchema(Schema):
    id = fields.Int(dump_only=True)
    user_id = fields.Int(allow_none=True)
    team_id = fields.Int(allow_none=True)
    type = fields.Str(required=True)
    severity = fields.Str(required=True)
    message = fields.Str(required=True)
    metadata = fields.Dict()
    is_read = fields.Bool()
    is_dismissed = fields.Bool()
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
