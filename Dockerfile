FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY pyproject.toml uv.lock ./
RUN pip install uv && uv sync --frozen

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 cloudotp && chown -R cloudotp:cloudotp /app
USER cloudotp

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/healthcheck || exit 1

# Run application
CMD ["gunicorn", "--config", "gunicorn.conf.py", "main:app"]

# To run a Celery worker, build the image and run with the following command:
# docker run <image_name> uv run celery -A main.celery worker -l info 