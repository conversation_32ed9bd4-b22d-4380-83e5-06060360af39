"""Analytics and dashboard endpoints for admins."""
from __future__ import annotations

from http import HTTPStatus
from datetime import datetime, timedelta
from typing import Any

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required
from sqlalchemy import func, and_, or_

from ..extensions import db
from ..models import (
    User, Team, OTP, APIKey, Coupon, Invitation, AuditLog, 
    Ticket, Payment, OTPShare, SecondaryPasswordAttempt, WebhookEvent, Campaign, Referral
)
from ..routes.admin_helpers import admin_required, sales_required, marketing_required
from ..schemas import CampaignSchema
from ..services.analytics_service import AnalyticsService

analytics_bp = Blueprint("analytics", __name__)

campaign_schema = CampaignSchema()
campaigns_schema = CampaignSchema(many=True)


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


def get_date_range(period: str) -> tuple[datetime, datetime]:
    """Get start and end dates for a given period."""
    end_date = datetime.utcnow()
    
    if period == "today":
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == "week":
        start_date = end_date - timedelta(days=7)
    elif period == "month":
        start_date = end_date - timedelta(days=30)
    elif period == "quarter":
        start_date = end_date - timedelta(days=90)
    elif period == "year":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    return start_date, end_date


@analytics_bp.route("/analytics/dashboard/overview", methods=["GET"])
@jwt_required()
@admin_required
def get_dashboard_overview():
    """Get high-level overview statistics for admin dashboard."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed (user segment, etc.)
    }
    data = AnalyticsService.get_dashboard_overview(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/dashboard/user-growth", methods=["GET"])
@jwt_required()
@admin_required
def get_user_growth():
    """Get user growth data over time."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_user_growth(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/dashboard/api-usage", methods=["GET"])
@jwt_required()
@admin_required
def get_api_usage():
    """Get API usage statistics."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_api_usage(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/dashboard/security", methods=["GET"])
@jwt_required()
@admin_required
def get_security_stats():
    """Get security-related statistics (2FA, failed logins, lockouts, etc.)."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_security_stats(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/sales/overview", methods=["GET"])
@jwt_required()
@sales_required
def get_sales_overview():
    """Get sales analytics and conversion metrics."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_sales_overview(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/marketing/overview", methods=["GET"])
@jwt_required()
@marketing_required
def get_marketing_overview():
    """Get marketing analytics and campaign performance."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_marketing_overview(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/reports/user-activity", methods=["GET"])
@jwt_required()
@admin_required
def get_user_activity_report():
    """Get detailed user activity report."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_user_activity_report(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/reports/system-health", methods=["GET"])
@jwt_required()
@admin_required
def get_system_health():
    """Get system health and performance metrics, including DB connection pool stats, Celery queue length, and Redis memory usage."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.get_system_health(filters)
    return jsonify(data)


@analytics_bp.route("/analytics/export/users", methods=["GET"])
@jwt_required()
@admin_required
def export_users():
    """Export user data for analytics or compliance."""
    period = request.args.get("period", "month")
    start_date, end_date = get_date_range(period)
    filters = {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        # Add more filters as needed
    }
    data = AnalyticsService.export_users(filters)
    return jsonify(data)