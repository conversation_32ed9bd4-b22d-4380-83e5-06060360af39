# -----------------------------------------------------------------------------
# Cloud OTP Backend Environment Variables
#
# This file serves as a template for the required and optional environment
# variables needed to run the application.
#
# Copy this file to .env and fill in the values for your environment.
# Do NOT commit the .env file to version control.
# -----------------------------------------------------------------------------

# =============================================================================
# GENERAL APPLICATION SETTINGS
# =============================================================================

# (Required for Production) A long, random, and secret string used for signing sessions and other security-related functions.
# Generate one with: python -c 'import secrets; print(secrets.token_hex(32))'
SECRET_KEY=your-super-secret-key-for-flask-sessions

# (Optional) Sets the application environment. Defaults to 'production'.
# Use 'development' for debugging features.
FLASK_ENV=development

# (Optional) A comma-separated list of allowed origins for Cross-Origin Resource Sharing (CORS).
# Use '*' for development only. For production, specify your frontend domain(s).
# Example: CORS_ORIGINS=http://localhost:3000,https://my-frontend.com
CORS_ORIGINS=*


# =============================================================================
# SECURITY & ENCRYPTION
# =============================================================================

# (Required for Production) A separate secret key for signing JSON Web Tokens (JWT).
# Should be different from SECRET_KEY.
JWT_SECRET_KEY=your-super-secret-key-for-jwt-tokens

# (Optional) A server-side encryption key for any additional encryption needs.
# Not currently used for core vault encryption but reserved for future features.
ENCRYPTION_KEY=


# =============================================================================
# DATABASE
# =============================================================================

# (Required) The full connection URI for your database.
# Defaults to a local SQLite file if not set, which is NOT recommended for production.
# Example for PostgreSQL: DATABASE_URL=postgresql://user:password@host:port/dbname
# Example for SQLite (dev): DATABASE_URL=sqlite:///cloud_otp_dev.db
DATABASE_URL=postgresql://cloudotp_user:cloudotp_pass@localhost:5432/cloudotp_db


# =============================================================================
# CACHE & BACKGROUND JOBS (Redis & Celery)
# =============================================================================

# (Required) The connection URI for your Redis instance. Used for rate limiting, caching, and as a Celery message broker.
REDIS_URL=redis://localhost:6379/0

# (Required) The connection URI for the Celery message broker. Can be the same as REDIS_URL but using a different DB number.
CELERY_BROKER_URL=redis://localhost:6379/1

# (Required) The connection URI for the Celery result backend. Can be the same as REDIS_URL but using a different DB number.
CELERY_RESULT_BACKEND=redis://localhost:6379/2


# =============================================================================
# API, RATE LIMITING & DEFAULTS
# =============================================================================

# (Optional) Default API requests per second for new users/teams. Defaults to 10.
DEFAULT_RATE_LIMIT_PER_SECOND=10

# (Optional) Default API requests per month for new users/teams. Defaults to 1000.
DEFAULT_API_QUOTA_PER_MONTH=1000

# (Optional) Default credits assigned to new users. Defaults to 100.
DEFAULT_USER_CREDITS=100

# (Optional) Default credits assigned to new teams. Defaults to 500.
DEFAULT_TEAM_CREDITS=500


# =============================================================================
# OAUTH 2.0 PROVIDERS (ALL OPTIONAL)
#
# Enable social logins by providing the client ID and secret for each provider.
# If a provider's keys are not set, it will not be available for login.
# =============================================================================

# --- Google ---
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# --- GitHub ---
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# --- Microsoft (Azure AD) ---
MICROSOFT_CLIENT_ID=
MICROSOFT_CLIENT_SECRET=

# --- LinkedIn ---
LINKEDIN_CLIENT_ID=
LINKEDIN_CLIENT_SECRET=

# --- Facebook ---
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=

# --- Generic OIDC Provider ---
OIDC_DISCOVERY_URL=
OIDC_CLIENT_ID=
OIDC_CLIENT_SECRET=


# =============================================================================
# ENTERPRISE SSO (SAML)
#
# These settings are for the Service Provider (SP) side of SAML.
# =============================================================================

# (Required for SAML) The directory to store generated/uploaded SAML keys and certificates.
# The application needs write access to this directory.
SAML_KEY_DIR=./saml_keys

# (Required for SAML) The path to the xmlsec1 binary, used for XML signature operations.
# Find it with `which xmlsec1` on your system.
XMLSEC_BINARY=/usr/bin/xmlsec1


# =============================================================================
# BILLING (PADDLE) (ALL OPTIONAL)
#
# Enable payment processing by providing your Paddle credentials.
# =============================================================================

PADDLE_VENDOR_ID=
PADDLE_API_KEY=
PADDLE_PUBLIC_KEY=
PADDLE_WEBHOOK_SECRET=

# (Optional) Set to 'false' to use Paddle's live environment. Defaults to 'true' (sandbox).
PADDLE_SANDBOX=true


# =============================================================================
# EMAIL (SMTP) (ALL OPTIONAL)
#
# Required for features like team invitations and system alerts.
# =============================================================================

MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=your-smtp-username
MAIL_PASSWORD=your-smtp-password
MAIL_DEFAULT_SENDER="Cloud OTP <<EMAIL>>"


# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# (Optional) Your Sentry DSN to enable error tracking.
SENTRY_DSN=

# (Optional) The environment name for Sentry (e.g., 'production', 'staging'). Defaults to 'production'.
SENTRY_ENVIRONMENT=production

# (Optional) Sentry performance monitoring sample rate. Defaults to 0.1 (10%).
SENTRY_TRACES_SAMPLE_RATE=0.1

# (Optional) Log level for the application. Can be DEBUG, INFO, WARNING, ERROR. Defaults to 'INFO'.
LOG_LEVEL=INFO

# (Optional) Path to the log file. Defaults to 'cloud_otp.log' in the root directory.
LOG_FILE=cloud_otp.log

# (Optional) Directory for Prometheus to store multi-process metrics. Required if running with multiple Gunicorn workers.
PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_metrics


# =============================================================================
# FEATURE FLAGS (ALL OPTIONAL)
#
# These flags can be used to disable major features. All default to 'true'.
# =============================================================================

FEATURE_SOCIAL_LOGIN=true
FEATURE_SSO=true
FEATURE_WEBHOOKS=true
FEATURE_ANALYTICS=true
FEATURE_RATE_LIMITING=true