"""Custom decorators for validation, rate limiting, and security."""
from __future__ import annotations

from functools import wraps
from http import HTT<PERSON>tatus
from typing import Callable, Any

from flask import request, jsonify, current_app
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from marshmallow import ValidationError

from .extensions import limiter
from .models import User


def validate_json(schema_class):
    """Decorator to validate JSON request data using Marshmallow schemas."""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                # Get JSON data
                json_data = request.get_json()
                if json_data is None:
                    return jsonify({"msg": "Request must contain valid JSON"}), HTTPStatus.BAD_REQUEST
                
                # Validate using schema
                schema = schema_class()
                validated_data = schema.load(json_data)
                
                # Add validated data to kwargs
                kwargs['validated_data'] = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return jsonify({
                    "msg": "Validation failed",
                    "errors": e.messages
                }), HTTPStatus.BAD_REQUEST
            except Exception as e:
                current_app.logger.error(f"Validation error: {e}")
                return jsonify({"msg": "Invalid request data"}), HTTPStatus.BAD_REQUEST
        
        return wrapper
    return decorator


def validate_query_params(schema_class):
    """Decorator to validate query parameters using Marshmallow schemas."""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                # Get query parameters
                query_data = request.args.to_dict()
                
                # Validate using schema
                schema = schema_class()
                validated_data = schema.load(query_data)
                
                # Add validated data to kwargs
                kwargs['validated_params'] = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return jsonify({
                    "msg": "Invalid query parameters",
                    "errors": e.messages
                }), HTTPStatus.BAD_REQUEST
            except Exception as e:
                current_app.logger.error(f"Query validation error: {e}")
                return jsonify({"msg": "Invalid query parameters"}), HTTPStatus.BAD_REQUEST
        
        return wrapper
    return decorator


def rate_limit_by_user(limit: str):
    """Rate limit decorator that applies limits per authenticated user."""
    def decorator(f: Callable) -> Callable:
        @limiter.limit(limit, key_func=lambda: f"user:{get_jwt_identity()}")
        @wraps(f)
        def wrapper(*args, **kwargs):
            return f(*args, **kwargs)
        return wrapper
    return decorator


def rate_limit_by_ip(limit: str):
    """Rate limit decorator that applies limits per IP address."""
    def decorator(f: Callable) -> Callable:
        @limiter.limit(limit)
        @wraps(f)
        def wrapper(*args, **kwargs):
            return f(*args, **kwargs)
        return wrapper
    return decorator


def api_quota_required(f: Callable) -> Callable:
    """Decorator to check API quota before processing requests."""
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            # Verify JWT and get user
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            
            if not user_id:
                return jsonify({"msg": "Authentication required"}), HTTPStatus.UNAUTHORIZED
            
            user = User.query.get(user_id)
            if not user:
                return jsonify({"msg": "User not found"}), HTTPStatus.NOT_FOUND
            
            # Check API quota
            from .extensions import redis_client
            
            # Get current month's usage
            from datetime import datetime
            current_month = datetime.utcnow().strftime("%Y-%m")
            usage_key = f"api_usage:monthly:user:{user_id}:{current_month}"
            
            current_usage = redis_client.get(usage_key)
            current_usage = int(current_usage) if current_usage else 0
            
            # Get user's quota limit
            quota_limit = user.api_quota_per_month or current_app.config.get("DEFAULT_API_QUOTA_PER_MONTH", 1000)
            
            if current_usage >= quota_limit:
                return jsonify({
                    "msg": "API quota exceeded",
                    "quota_limit": quota_limit,
                    "current_usage": current_usage
                }), HTTPStatus.TOO_MANY_REQUESTS
            
            # Increment usage counter
            redis_client.incr(usage_key)
            redis_client.expire(usage_key, 32 * 24 * 3600)  # Expire after 32 days
            
            # Add quota info to response headers
            from flask import g
            g.api_quota_limit = quota_limit
            g.api_quota_used = current_usage + 1
            g.api_quota_remaining = quota_limit - (current_usage + 1)
            
            return f(*args, **kwargs)
            
        except Exception as e:
            current_app.logger.error(f"API quota check failed: {e}")
            return jsonify({"msg": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR
    
    return wrapper


def add_quota_headers(response):
    """Add API quota headers to response."""
    from flask import g
    
    if hasattr(g, 'api_quota_limit'):
        response.headers['X-RateLimit-Limit'] = str(g.api_quota_limit)
        response.headers['X-RateLimit-Used'] = str(g.api_quota_used)
        response.headers['X-RateLimit-Remaining'] = str(g.api_quota_remaining)
    
    return response


def require_feature_flag(feature_name: str):
    """Decorator to check if a feature flag is enabled."""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            if not current_app.config.get(f"FEATURE_{feature_name.upper()}", True):
                return jsonify({"msg": f"Feature '{feature_name}' is not enabled"}), HTTPStatus.NOT_IMPLEMENTED
            return f(*args, **kwargs)
        return wrapper
    return decorator


def log_api_request(f: Callable) -> Callable:
    """Decorator to log API requests for audit purposes."""
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            # Get request info
            user_id = None
            try:
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
            except:
                pass
            
            # Log request
            current_app.logger.info(
                f"API Request: {request.method} {request.path}",
                extra={
                    "user_id": user_id,
                    "ip_address": request.remote_addr,
                    "user_agent": request.headers.get("User-Agent"),
                    "endpoint": request.endpoint,
                    "method": request.method,
                    "path": request.path,
                    "query_params": dict(request.args),
                }
            )
            
            return f(*args, **kwargs)
            
        except Exception as e:
            current_app.logger.error(f"Request logging failed: {e}")
            return f(*args, **kwargs)  # Don't fail the request if logging fails
    
    return wrapper


def security_headers(f: Callable) -> Callable:
    """Decorator to add security headers to responses."""
    @wraps(f)
    def wrapper(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Add security headers
        if hasattr(response, 'headers'):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            response.headers['Content-Security-Policy'] = "default-src 'self'"
        
        return response
    
    return wrapper
