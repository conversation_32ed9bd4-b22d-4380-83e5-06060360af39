from celery import shared_task
from .extensions import celery, redis_client, db, mail
from .models import WebhookEvent, User, Team, Notification, SystemSetting, AuditLog, Role
from .schemas import NotificationSchema
from flask_mail import Message
from datetime import datetime, timedelta
import requests
import logging
from .services.analytics_service import AnalyticsService
from sqlalchemy import or_

@shared_task(bind=True, max_retries=5, default_retry_delay=300)
def deliver_webhook(self, webhook_event_id: int):
    """
    Celery task to deliver a webhook.
    Uses exponential backoff for retries.
    """
    webhook_event = WebhookEvent.query.get(webhook_event_id)
    if not webhook_event:
        logging.warning(f"WebhookEvent with id {webhook_event_id} not found.")
        return

    webhook_event.attempts += 1
    webhook_event.last_attempt_at = datetime.utcnow()

    try:
        webhook_payload = {
            "event_type": webhook_event.event_type,
            "timestamp": webhook_event.created_at.isoformat() + "Z",
            "data": webhook_event.payload
        }
        
        response = requests.post(
            webhook_event.target_url,
            json=webhook_payload,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "CloudOTP-Webhook/1.0"
            },
            timeout=30
        )
        
        webhook_event.response_status = response.status_code
        webhook_event.response_body = response.text[:1000]

        if 200 <= response.status_code < 300:
            webhook_event.status = "sent"
            db.session.commit()
            logging.info(f"Webhook {webhook_event.id} delivered successfully to {webhook_event.target_url}")
            return
        else:
            webhook_event.status = "failed"
            db.session.commit()
            raise requests.exceptions.HTTPError(f"Webhook delivery failed with status {response.status_code}")

    except Exception as exc:
        logging.error(f"Webhook {webhook_event.id} failed to deliver: {exc}")
        webhook_event.status = "failed"
        db.session.commit()
        retry_delay = 60 * (2 ** self.request.retries)
        raise self.retry(exc=exc, countdown=retry_delay)

@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_email(self, message_data: dict):
    """Celery task to send an email."""
    try:
        msg = Message(**message_data)
        mail.send(msg)
        logging.info(f"Email sent to {message_data['recipients']}")
    except Exception as exc:
        logging.error(f"Failed to send email: {exc}")
        raise self.retry(exc=exc)

@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    from celery.schedules import crontab
    
    # Run analytics aggregation every hour
    sender.add_periodic_task(3600.0, aggregate_analytics.s(), name='Aggregate analytics data every hour')
    
    # Check system health every 5 minutes
    sender.add_periodic_task(300.0, check_system_alerts.s(), name='Check system alerts every 5 minutes')
    
    # Reset monthly API quotas on the first day of each month
    sender.add_periodic_task(
        crontab(minute=0, hour=0, day_of_month=1),
        reset_monthly_quotas.s(),
        name='Reset monthly API quotas'
    )
    
    # Clean up expired invitations and coupons daily at 2 AM
    sender.add_periodic_task(
        crontab(minute=0, hour=2),
        cleanup_expired_items.s(),
        name='Cleanup expired items daily'
    )
    
    # Process pending webhooks every minute
    sender.add_periodic_task(60.0, process_pending_webhooks.s(), name='Process pending webhooks')

@celery.task
def aggregate_analytics():
    """Aggregate analytics data and populate summary tables/caches."""
    # Example: Precompute dashboard overview for last month
    filters = {
        "period": "month",
        "start_date": datetime.utcnow() - timedelta(days=30),
        "end_date": datetime.utcnow(),
    }
    AnalyticsService.get_dashboard_overview(filters)
    # Add more precomputations as needed for other analytics endpoints
    # (e.g., user growth, API usage, security stats, etc.)

@celery.task
def check_system_alerts():
    """Periodic task to check system health and send alerts if thresholds are breached."""
    # Load thresholds from SystemSetting
    thresholds = {
        "failed_webhooks": 50,
        "error_rate": 5.0,  # percent
        "low_credits": 10,  # credits
        # Add more as needed
    }
    for key in thresholds:
        setting = SystemSetting.query.get(f"alert_threshold_{key}")
        if setting:
            thresholds[key] = setting.value

    # Gather system health metrics
    filters = {
        "period": "day",
        "start_date": datetime.utcnow() - timedelta(days=1),
        "end_date": datetime.utcnow(),
    }
    health = AnalyticsService.get_system_health(filters)
    alerts = []
    now = datetime.utcnow()
    # Check failed webhooks
    failed_webhooks = health["webhooks"]["failed"]
    if failed_webhooks >= thresholds["failed_webhooks"]:
        alerts.append({
            "type": "system_alert",
            "severity": "warning" if failed_webhooks < thresholds["failed_webhooks"] * 2 else "critical",
            "message": f"Failed webhooks: {failed_webhooks} (threshold: {thresholds['failed_webhooks']})",
            "metadata": {"metric": "failed_webhooks", "value": failed_webhooks, "threshold": thresholds["failed_webhooks"]},
        })
    # Check error rate
    error_rate = health["errors"]["error_rate"]
    if error_rate >= thresholds["error_rate"]:
        alerts.append({
            "type": "system_alert",
            "severity": "warning" if error_rate < thresholds["error_rate"] * 2 else "critical",
            "message": f"System error rate: {error_rate}% (threshold: {thresholds['error_rate']}%)",
            "metadata": {"metric": "error_rate", "value": error_rate, "threshold": thresholds["error_rate"]},
        })
    # Check low credits for key accounts
    low_credit_users = User.query.filter(User.credits <= thresholds["low_credits"], User.is_active == True).all()
    for user in low_credit_users:
        alerts.append({
            "type": "credit_low",
            "severity": "warning",
            "message": f"User {user.email} has low credits: {user.credits}",
            "metadata": {"user_id": user.id, "credits": user.credits, "threshold": thresholds["low_credits"]},
        })
    # Deduplicate: don't send duplicate alerts for same incident within 1 hour
    for alert in alerts:
        recent = Notification.query.filter(
            Notification.type == alert["type"],
            Notification.severity == alert["severity"],
            Notification.message == alert["message"],
            Notification.created_at >= now - timedelta(hours=1)
        ).first()
        if recent:
            continue  # Skip duplicate
        # Send email to Super/Developer Admins
        admin_roles = ["Super Admin", "Developer Admin"]
        admins = User.query.join(User.roles).filter(Role.name.in_(admin_roles), User.is_active == True).all()
        emails = [a.email for a in admins if a.email]
        if emails:
            try:
                msg = Message(
                    subject=f"[Cloud OTP] {alert['severity'].capitalize()} Alert: {alert['message'][:60]}",
                    recipients=emails,
                    body=f"System Alert:\n\n{alert['message']}\n\nDetails: {alert['metadata']}\nTime: {now.isoformat()}"
                )
                mail.send(msg)
            except Exception as exc:
                logging.error(f"Failed to send alert email: {exc}")
        # Create in-app Notification for all admins
        for admin in admins:
            notif = Notification(
                user_id=admin.id,
                type=alert["type"],
                severity=alert["severity"],
                message=alert["message"],
                metadata=alert["metadata"],
                is_read=False,
                is_dismissed=False,
            )
            db.session.add(notif)
        db.session.commit()
        # Audit log
        for admin in admins:
            log = AuditLog(
                user_id=admin.id,
                action="system_alert_sent",
                details={"alert": alert}
            )
            db.session.add(log)
        db.session.commit()


@celery.task
def reset_monthly_quotas():
    """Reset monthly API quotas for all users and teams."""
    from .extensions import redis_client
    
    try:
        # Reset Redis-based quota counters
        # Pattern: api_usage:monthly:user:{user_id} or api_usage:monthly:team:{team_id}
        monthly_keys = redis_client.keys("api_usage:monthly:*")
        if monthly_keys:
            redis_client.delete(*monthly_keys)
            logging.info(f"Reset {len(monthly_keys)} monthly quota counters")
        
        # Reset database-based usage tracking if any
        from .models import User, Team
        User.query.update({"monthly_api_usage": 0})
        Team.query.update({"monthly_api_usage": 0})
        db.session.commit()
        
        logging.info("Monthly API quotas reset successfully")
        
    except Exception as e:
        logging.error(f"Failed to reset monthly quotas: {e}")
        raise


@celery.task
def cleanup_expired_items():
    """Clean up expired invitations, coupons, and other time-limited items."""
    from .models import Invitation, Coupon, OTPShare, FolderShare
    
    now = datetime.utcnow()
    cleanup_count = 0
    
    try:
        # Deactivate expired invitations
        expired_invitations = Invitation.query.filter(
            Invitation.expires_at < now,
            Invitation.is_active == True
        ).all()
        
        for invitation in expired_invitations:
            invitation.is_active = False
            invitation.status = "expired"
        cleanup_count += len(expired_invitations)
        
        # Deactivate expired coupons
        expired_coupons = Coupon.query.filter(
            Coupon.expires_at < now,
            Coupon.is_active == True
        ).all()
        
        for coupon in expired_coupons:
            coupon.is_active = False
        cleanup_count += len(expired_coupons)
        
        # Deactivate expired OTP shares
        expired_otp_shares = OTPShare.query.filter(
            OTPShare.expires_at < now,
            OTPShare.is_active == True
        ).all()
        
        for share in expired_otp_shares:
            share.is_active = False
        cleanup_count += len(expired_otp_shares)
        
        # Deactivate expired folder shares
        expired_folder_shares = FolderShare.query.filter(
            FolderShare.expires_at < now,
            FolderShare.is_active == True
        ).all()
        
        for share in expired_folder_shares:
            share.is_active = False
        cleanup_count += len(expired_folder_shares)
        
        db.session.commit()
        logging.info(f"Cleaned up {cleanup_count} expired items")
        
    except Exception as e:
        logging.error(f"Failed to cleanup expired items: {e}")
        db.session.rollback()
        raise


@celery.task
def process_pending_webhooks():
    """Process pending webhook events that need to be delivered."""
    from .models import WebhookEvent
    
    try:
        # Get pending webhooks (not sent and not failed too many times)
        pending_webhooks = WebhookEvent.query.filter(
            WebhookEvent.status == "pending",
            WebhookEvent.attempts < 5
        ).limit(100).all()  # Process in batches
        
        for webhook in pending_webhooks:
            # Trigger async delivery
            deliver_webhook.delay(webhook.id)
        
        if pending_webhooks:
            logging.info(f"Queued {len(pending_webhooks)} pending webhooks for delivery")
            
    except Exception as e:
        logging.error(f"Failed to process pending webhooks: {e}")
        raise


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_email(self, to: str, subject: str, body: str, html_body: str = None):
    """Enhanced email sending task with proper error handling."""
    try:
        msg = Message(
            subject=subject,
            recipients=[to] if isinstance(to, str) else to,
            body=body,
            html=html_body
        )
        mail.send(msg)
        logging.info(f"Email sent successfully to {to}")
        
    except Exception as exc:
        logging.error(f"Failed to send email to {to}: {exc}")
        raise self.retry(exc=exc)