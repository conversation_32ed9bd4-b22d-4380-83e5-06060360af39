"""Rate limiting and API quota management using Redis."""
from __future__ import annotations

from http import HTTPStatus
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Callable, NamedTuple
import json

from flask import Blueprint, jsonify, request, g, current_app
from flask_jwt_extended import get_jwt_identity, jwt_required, verify_jwt_in_request

from ..extensions import db, redis_client
from ..models import User, Team, APIKey, AuditLog
from ..routes.admin_helpers import admin_required, audit_log

rate_limiting_bp = Blueprint("rate_limiting", __name__)


class RateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded."""
    pass


class QuotaExceeded(Exception):
    """Exception raised when API quota is exceeded."""
    pass


class RateLimitEntity(NamedTuple):
    """Helper class to hold rate limit subject and its limits."""

    id: str
    rate_limit_per_second: int
    api_quota_per_month: int
    is_team: bool = False


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


def get_rate_limit_key(user_id: int, endpoint: str) -> str:
    """Generate a rate limit key for Redis or in-memory storage."""
    return f"rate_limit:{user_id}:{endpoint}"


def get_quota_key(user_id: int, period: str = "month") -> str:
    """Generate a quota key for tracking API usage."""
    now = datetime.utcnow()
    if period == "month":
        period_key = f"{now.year}-{now.month:02d}"
    elif period == "day":
        period_key = f"{now.year}-{now.month:02d}-{now.day:02d}"
    else:
        period_key = f"{now.year}-{now.month:02d}"
    
    return f"quota:{user_id}:{period_key}"


def get_entity_from_jwt() -> RateLimitEntity | None:
    """Get the rate limit entity from a JWT token."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user:
            return None
        return RateLimitEntity(
            id=f"user:{user.id}",
            rate_limit_per_second=user.api_rate_limit_per_second
            or current_app.config["DEFAULT_RATE_LIMIT_PER_SECOND"],
            api_quota_per_month=user.api_quota_per_month
            or current_app.config["DEFAULT_API_QUOTA_PER_MONTH"],
        )
    except Exception:
        return None


def get_entity_from_api_key() -> RateLimitEntity | None:
    """Get the rate limit entity from an API key."""
    api_key_header = request.headers.get("X-API-Key")
    if not api_key_header:
        return None

    prefix, _, key = api_key_header.partition("_")
    key_hash = APIKey.hash_key(api_key_header)
    api_key_obj = APIKey.query.filter_by(key_hash=key_hash, prefix=prefix).first()

    if not api_key_obj or not api_key_obj.is_active:
        return None

    # Update last used timestamp
    api_key_obj.last_used_at = datetime.utcnow()
    db.session.add(api_key_obj)
    db.session.commit()

    if api_key_obj.user:
        entity = api_key_obj.user
        entity_id = f"user:{entity.id}"
        is_team = False
    elif api_key_obj.team:
        entity = api_key_obj.team
        entity_id = f"team:{entity.id}"
        is_team = True
    else:
        return None

    g.api_key_obj = api_key_obj  # Store for logging if needed
    g.current_user = entity if not is_team else entity.owner
    g.current_team = entity if is_team else None

    return RateLimitEntity(
        id=entity_id,
        rate_limit_per_second=entity.api_rate_limit_per_second
        or current_app.config["DEFAULT_RATE_LIMIT_PER_SECOND"],
        api_quota_per_month=entity.api_quota_per_month
        or current_app.config["DEFAULT_API_QUOTA_PER_MONTH"],
        is_team=is_team,
    )


def get_keys(entity_id: str, endpoint: str) -> tuple[str, str, str, str]:
    """Generate the set of Redis keys for an entity and endpoint."""
    now = datetime.utcnow()
    current_second = int(now.timestamp())

    rate_limit_key = f"rate_limit:{entity_id}:{endpoint}:{current_second}"

    month_key = f"quota:{entity_id}:{now.year}-{now.month:02d}"
    day_key = f"quota_daily:{entity_id}:{now.year}-{now.month:02d}-{now.day:02d}"
    endpoint_key = f"quota_endpoint:{entity_id}:{now.year}-{now.month:02d}"

    return rate_limit_key, month_key, day_key, endpoint_key


def check_limits_and_increment(
    entity: RateLimitEntity, endpoint: str
) -> tuple[int, int]:
    """Check rate limit and quota, and increment counters in Redis.

    Returns a tuple of (current_quota_usage, quota_limit).
    Raises RateLimitExceeded or QuotaExceeded on violation.
    """
    rate_key, month_key, day_key, endpoint_key = get_keys(entity.id, endpoint)

    pipe = redis_client.pipeline()
    pipe.incr(rate_key)
    pipe.expire(rate_key, 5)  # Expire after 5 seconds to be safe
    rate_count, _ = pipe.execute()

    if rate_count > entity.rate_limit_per_second:
        redis_client.incr(f"violations:rate_limit:{entity.id}:{datetime.utcnow().date()}")
        raise RateLimitExceeded()

    current_quota = redis_client.get(month_key)
    current_quota = int(current_quota) if current_quota else 0

    if current_quota >= entity.api_quota_per_month:
        redis_client.incr(f"violations:quota:{entity.id}:{datetime.utcnow().date()}")
        raise QuotaExceeded()

    pipe = redis_client.pipeline()
    pipe.incr(month_key)
    pipe.incr(day_key)
    pipe.hincrby(endpoint_key, endpoint, 1)

    pipe.expire(month_key, timedelta(days=35))
    pipe.expire(day_key, timedelta(days=2))
    pipe.expire(endpoint_key, timedelta(days=35))

    results = pipe.execute()
    new_quota_usage = results[0]

    return new_quota_usage, entity.api_quota_per_month


def log_api_request(user: User, endpoint: str, method: str) -> None:
    """Log an API request for rate limiting and quota tracking."""
    log_entry = AuditLog(
        user_id=user.id,
        action=f"API_REQUEST:{endpoint}",
        details={
            "method": method,
            "endpoint": endpoint,
            "ip_address": request.remote_addr,
            "user_agent": request.headers.get("User-Agent")
        }
    )
    db.session.add(log_entry)
    db.session.commit()


def rate_limit_required(endpoint: str = None):
    """Decorator to enforce rate limiting on API endpoints."""
    def decorator(f: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(f)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            entity = get_entity_from_jwt()
            if not entity:
                return jsonify({"msg": "Authentication required"}), HTTPStatus.UNAUTHORIZED

            endpoint_name = endpoint or f.__name__

            try:
                used, total = check_limits_and_increment(entity, endpoint_name)
                g.quota_used = used
                g.quota_total = total
            except RateLimitExceeded:
                return jsonify({
                    "msg": "Rate limit exceeded",
                    "limit": entity.rate_limit_per_second,
                    "retry_after": 1,
                }), HTTPStatus.TOO_MANY_REQUESTS
            except QuotaExceeded:
                used = (
                    redis_client.get(get_keys(entity.id, endpoint_name)[1])
                    or entity.api_quota_per_month
                )
                return jsonify({
                    "msg": "Monthly quota exceeded",
                    "quota_used": int(used),
                    "quota_total": entity.api_quota_per_month,
                }), HTTPStatus.TOO_MANY_REQUESTS

            return f(*args, **kwargs)

        return wrapper
    return decorator


def api_key_rate_limit_required(endpoint: str = None): # noqa: D401
    """Decorator to enforce rate limiting for API key or JWT authentication."""
    def decorator(f: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(f)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            g.auth_method = None
            g.current_user = None
            g.current_team = None
            
            api_key_header = request.headers.get("X-API-Key")
            
            if api_key_header:
                g.auth_method = "api_key"
                entity = get_entity_from_api_key()
                if not entity:
                    return (jsonify({"msg": "Invalid or missing API key"}), HTTPStatus.UNAUTHORIZED)
            else:
                try:
                    verify_jwt_in_request()
                    g.auth_method = "jwt"
                    entity = get_entity_from_jwt()
                    if not entity:
                        return jsonify({"msg": "Authentication required"}), HTTPStatus.UNAUTHORIZED
                    g.current_user = User.query.get(get_jwt_identity())
                except Exception:
                    return jsonify({"msg": "Missing or invalid JWT"}), HTTPStatus.UNAUTHORIZED

            endpoint_name = endpoint or f.__name__

            try:
                used, total = check_limits_and_increment(entity, endpoint_name)
                g.quota_used = used
                g.quota_total = total
            except RateLimitExceeded:
                return (
                    jsonify({
                        "msg": "API rate limit exceeded",
                        "limit": entity.rate_limit_per_second,
                        "retry_after": 1,
                    }),
                    HTTPStatus.TOO_MANY_REQUESTS,
                )
            except QuotaExceeded:
                used = (
                    redis_client.get(get_keys(entity.id, endpoint_name)[1])
                    or entity.api_quota_per_month
                )
                return (
                    jsonify({
                        "msg": "Monthly API quota exceeded",
                        "quota_used": int(used),
                        "quota_total": entity.api_quota_per_month,
                    }),
                    HTTPStatus.TOO_MANY_REQUESTS,
                )

            if not g.current_user:
                return jsonify({"msg": "Authentication failed"}), HTTPStatus.UNAUTHORIZED

            return f(*args, **kwargs)

        return wrapper
    return decorator


@rate_limiting_bp.route("/status", methods=["GET"])
@jwt_required()
def get_rate_limit_status():
    """Get current rate limit and quota status for the user."""
    entity = get_entity_from_jwt()
    if not entity:
        return jsonify({"msg": "User not found"}), HTTPStatus.NOT_FOUND

    month_key = get_keys(entity.id, "")[1]
    used = int(redis_client.get(month_key) or 0)
    total = entity.api_quota_per_month

    now = datetime.utcnow()
    rate_key = f"rate_limit:{entity.id}:get_rate_limit_status:{int(now.timestamp())}"
    current_rate_usage = int(redis_client.get(rate_key) or 0)

    # Calculate quota reset date (next month)
    if now.month == 12:
        next_month = now.replace(
            year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0
        )
    else:
        next_month = now.replace(
            month=now.month + 1, day=1, hour=0, minute=0, second=0, microsecond=0
        )

    return jsonify({
        "rate_limit": {
            "requests_per_second": entity.rate_limit_per_second,
            "current_usage": current_rate_usage,
        },
        "quota": {
            "monthly_limit": total,
            "used": used,
            "remaining": total - used,
            "percentage_used": round((used / total) * 100, 2) if total > 0 else 0,
            "resets_at": next_month.isoformat() + "Z",
        },
    })


@rate_limiting_bp.route("/usage-history", methods=["GET"])
@jwt_required()
def get_usage_history():
    """Get API usage history for the current user from Redis."""
    entity = get_entity_from_jwt()
    if not entity:
        return jsonify({"msg": "User not found"}), HTTPStatus.NOT_FOUND

    days = min(int(request.args.get("days", 30)), 90)  # Max 90 days

    daily_keys = []
    dates = []
    for i in range(days):
        date = datetime.utcnow() - timedelta(days=i)
        dates.append(date.date())
        daily_keys.append(
            f"quota_daily:{entity.id}:{date.year}-{date.month:02d}-{date.day:02d}"
        )

    daily_counts = [int(c or 0) for c in redis_client.mget(daily_keys)]

    daily_usage_data = [
        {"date": d.isoformat(), "requests": c}
        for d, c in zip(reversed(dates), reversed(daily_counts))
    ]

    now = datetime.utcnow()
    endpoint_key = f"quota_endpoint:{entity.id}:{now.year}-{now.month:02d}"
    endpoint_counts = redis_client.hgetall(endpoint_key)

    endpoint_usage_data = [
        {"endpoint": e.decode(), "requests": int(c)} for e, c in endpoint_counts.items()
    ]
    endpoint_usage_data.sort(key=lambda x: x["requests"], reverse=True)

    return jsonify({
        "period_days": days,
        "daily_usage": daily_usage_data,
        "endpoint_usage": endpoint_usage_data,
    })


@rate_limiting_bp.route("/admin/user-limits", methods=["GET"])
@jwt_required()
@admin_required
def get_user_limits():
    """Get rate limits and quotas for all users (admin only)."""
    users = User.query.all()

    user_limits = []
    now = datetime.utcnow()
    month_keys = [f"quota:user:{user.id}:{now.year}-{now.month:02d}" for user in users]

    if not month_keys:
        return jsonify({"user_limits": []})

    monthly_usage = redis_client.mget(month_keys)

    for i, user in enumerate(users):
        total = (
            user.api_quota_per_month
            or current_app.config["DEFAULT_API_QUOTA_PER_MONTH"]
        )
        used = int(monthly_usage[i] or 0)

        user_limits.append({
            "user_id": user.id,
            "email": user.email,
            "rate_limit_per_second": user.api_rate_limit_per_second
            or current_app.config["DEFAULT_RATE_LIMIT_PER_SECOND"],
            "quota_per_month": total,
            "quota_used": used,
            "quota_remaining": total - used,
            "quota_exceeded": used >= total,
        })

    return jsonify({"user_limits": user_limits})


@rate_limiting_bp.route("/admin/update-limits", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Updated user API limits")
def update_user_limits():
    """Update rate limits and quotas for a user (admin only)."""
    data = request.get_json() or {}
    
    user_id = data.get("user_id")
    if not user_id:
        return jsonify({"msg": "user_id is required"}), HTTPStatus.BAD_REQUEST
    
    user = User.query.get_or_404(user_id)
    
    if "api_rate_limit_per_second" in data:
        user.api_rate_limit_per_second = data["api_rate_limit_per_second"]
    
    if "api_quota_per_month" in data:
        user.api_quota_per_month = data["api_quota_per_month"]
    
    db.session.commit()
    
    return jsonify({
        "msg": f"Updated limits for {user.email}",
        "rate_limit_per_second": user.api_rate_limit_per_second,
        "quota_per_month": user.api_quota_per_month
    })


@rate_limiting_bp.route("/admin/reset-quota", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Reset user API quota from Redis")
def reset_user_quota():
    """Reset API quota for a user by deleting Redis keys (admin only)."""
    data = request.get_json() or {}

    user_id = data.get("user_id")
    if not user_id:
        return jsonify({"msg": "user_id is required"}), HTTPStatus.BAD_REQUEST

    user = User.query.get_or_404(user_id)
    entity_id = f"user:{user.id}"

    now = datetime.utcnow()
    month_key = f"quota:{entity_id}:{now.year}-{now.month:02d}"
    endpoint_key = f"quota_endpoint:{entity_id}:{now.year}-{now.month:02d}"

    redis_client.delete(month_key, endpoint_key)

    return jsonify({"msg": f"Reset quota for {user.email}"})


@rate_limiting_bp.route("/admin/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_rate_limiting_stats():
    """Get rate limiting and quota statistics from Redis (admin only)."""
    users_over_quota = 0
    total_api_requests = 0

    now = datetime.utcnow()
    month_str = f"{now.year}-{now.month:02d}"

    users = User.query.all()
    user_keys = [f"quota:user:{u.id}:{month_str}" for u in users]

    if user_keys:
        user_quotas = [int(q or 0) for q in redis_client.mget(user_keys)]

        for i, user in enumerate(users):
            used = user_quotas[i]
            total = (
                user.api_quota_per_month
                or current_app.config["DEFAULT_API_QUOTA_PER_MONTH"]
            )
            if used >= total:
                users_over_quota += 1
            total_api_requests += used

    today_str = now.date().isoformat()
    violation_keys = redis_client.keys(f"violations:*:{today_str}")
    recent_violations = (
        sum([int(v or 0) for v in redis_client.mget(violation_keys)])
        if violation_keys
        else 0
    )

    return jsonify({
        "users_over_quota": users_over_quota,
        "total_api_requests_this_month": total_api_requests,
        "recent_rate_limit_violations": int(recent_violations),
        "average_requests_per_user": round(total_api_requests / len(users), 2)
        if users
        else 0,
    })