"""Invitation management endpoints."""
from __future__ import annotations

from http import HTT<PERSON>tatus
from datetime import datetime, timedelta
import secrets

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import Invitation, User, Team, TeamMember
from ..schemas import InvitationSchema
from ..routes.admin_helpers import admin_required, sales_required, marketing_required, audit_log
from ..routes.webhooks import trigger_team_member_added_webhook

invitations_bp = Blueprint("invitations", __name__)

invitation_schema = InvitationSchema()
invitations_schema = InvitationSchema(many=True)


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


@invitations_bp.route("/", methods=["POST"])
@jwt_required()
@audit_log("Created invitation")
def create_invitation():
    """Create a new invitation code."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    # Check if user has permission to create invitations
    if not (current_user.has_role("Super Admin") or 
            current_user.has_role("Marketing") or 
            current_user.has_role("Sales")):
        # Team owners can create team invitations
        team_id = data.get("team_id")
        if team_id:
            team = Team.query.get_or_404(team_id)
            if team.owner_id != current_user.id:
                return jsonify({"msg": "Only team owners can create team invitations"}), HTTPStatus.FORBIDDEN
        else:
            return jsonify({"msg": "Insufficient permissions"}), HTTPStatus.FORBIDDEN
    
    # Generate unique invitation code
    code = f"inv_{secrets.token_urlsafe(16)}"
    
    # Set expiration (default 7 days)
    expires_at = None
    if data.get("expires_in_days"):
        expires_at = datetime.utcnow() + timedelta(days=data["expires_in_days"])
    elif not data.get("no_expiration"):
        expires_at = datetime.utcnow() + timedelta(days=7)
    
    invitation = Invitation(
        code=code,
        created_by_id=current_user.id,
        team_id=data.get("team_id"),
        expires_at=expires_at
    )
    
    db.session.add(invitation)
    db.session.commit()
    
    return invitation_schema.jsonify(invitation), HTTPStatus.CREATED


@invitations_bp.route("/", methods=["GET"])
@jwt_required()
def list_invitations():
    """List invitations created by the current user or accessible to them."""
    current_user = get_current_user()
    
    if current_user.has_role("Super Admin") or current_user.has_role("Marketing"):
        # Admins can see all invitations
        invitations = Invitation.query.all()
    else:
        # Regular users can only see their own invitations
        invitations = Invitation.query.filter_by(created_by_id=current_user.id).all()
    
    return invitations_schema.jsonify(invitations)


@invitations_bp.route("/<int:invitation_id>", methods=["GET"])
@jwt_required()
def get_invitation(invitation_id: int):
    """Get details of a specific invitation."""
    current_user = get_current_user()
    invitation = Invitation.query.get_or_404(invitation_id)
    
    # Check access permissions
    if not (current_user.has_role("Super Admin") or 
            current_user.has_role("Marketing") or
            invitation.created_by_id == current_user.id):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    return invitation_schema.jsonify(invitation)


@invitations_bp.route("/<int:invitation_id>", methods=["PUT"])
@jwt_required()
@audit_log("Updated invitation")
def update_invitation(invitation_id: int):
    """Update an invitation (e.g., deactivate it)."""
    current_user = get_current_user()
    invitation = Invitation.query.get_or_404(invitation_id)
    
    # Check permissions
    if not (current_user.has_role("Super Admin") or 
            current_user.has_role("Marketing") or
            invitation.created_by_id == current_user.id):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    
    if "is_active" in data:
        invitation.is_active = data["is_active"]
    
    if "expires_at" in data:
        invitation.expires_at = data["expires_at"]
    
    db.session.commit()
    return invitation_schema.jsonify(invitation)


@invitations_bp.route("/<int:invitation_id>", methods=["DELETE"])
@jwt_required()
@audit_log("Deleted invitation")
def delete_invitation(invitation_id: int):
    """Delete an invitation."""
    current_user = get_current_user()
    invitation = Invitation.query.get_or_404(invitation_id)
    
    # Check permissions
    if not (current_user.has_role("Super Admin") or 
            current_user.has_role("Marketing") or
            invitation.created_by_id == current_user.id):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    db.session.delete(invitation)
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


@invitations_bp.route("/use/<string:code>", methods=["POST"])
def use_invitation(code: str):
    """Use an invitation code to register or join a team."""
    invitation = Invitation.query.filter_by(code=code, is_active=True).first()
    
    if not invitation:
        return jsonify({"msg": "Invalid or expired invitation code"}), HTTPStatus.NOT_FOUND
    
    # Check if invitation has expired
    if invitation.expires_at and invitation.expires_at < datetime.utcnow():
        return jsonify({"msg": "Invitation has expired"}), HTTPStatus.GONE
    
    data = request.get_json() or {}
    
    # If this is for team invitation, user must be authenticated
    if invitation.team_id:
        # This would require JWT authentication
        # For now, return the invitation details for frontend to handle
        return jsonify({
            "invitation_type": "team",
            "team_id": invitation.team_id,
            "message": "Please log in to join the team"
        })
    
    # For general platform invitations, return success
    return jsonify({
        "invitation_type": "platform",
        "message": "Invitation is valid. Proceed with registration."
    })


@invitations_bp.route("/join-team/<string:code>", methods=["POST"])
@jwt_required()
@audit_log("Joined team via invitation")
def join_team_via_invitation(code: str):
    """Join a team using an invitation code."""
    current_user = get_current_user()
    invitation = Invitation.query.filter_by(code=code, is_active=True).first()
    
    if not invitation or not invitation.team_id:
        return jsonify({"msg": "Invalid team invitation"}), HTTPStatus.NOT_FOUND
    
    # Check if invitation has expired
    if invitation.expires_at and invitation.expires_at < datetime.utcnow():
        return jsonify({"msg": "Invitation has expired"}), HTTPStatus.GONE
    
    # Check if user is already a member
    existing_member = TeamMember.query.filter_by(
        team_id=invitation.team_id, user_id=current_user.id
    ).first()
    
    if existing_member:
        return jsonify({"msg": "You are already a member of this team"}), HTTPStatus.CONFLICT
    
    # Add user to team
    team_member = TeamMember(
        team_id=invitation.team_id,
        user_id=current_user.id,
        role="member"  # Default role for invited members
    )
    
    db.session.add(team_member)
    db.session.commit()
    
    trigger_team_member_added_webhook(
        team_id=team_member.team_id,
        user_id=team_member.user_id,
        role=team_member.role,
    )
    
    return jsonify({"msg": "Successfully joined the team"}), HTTPStatus.OK


# Admin endpoints for invitation management
@invitations_bp.route("/admin/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_invitation_stats():
    """Get invitation statistics for admin dashboard."""
    total_invitations = Invitation.query.count()
    active_invitations = Invitation.query.filter_by(is_active=True).count()
    expired_invitations = Invitation.query.filter(
        Invitation.expires_at < datetime.utcnow()
    ).count()
    
    return jsonify({
        "total_invitations": total_invitations,
        "active_invitations": active_invitations,
        "expired_invitations": expired_invitations
    })