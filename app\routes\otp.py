"""OTP management endpoints with full CRUD, sharing, and collaboration features."""
from __future__ import annotations

from http import HTT<PERSON>tatus
from typing import Any
import secrets

from flask import Blueprint, jsonify, request, g
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import OTP, User, Team, TeamMember, AuditLog, OTPShare, Folder, Note, FolderShare
from ..schemas import OTPSchema, OTPShareSchema, FolderSchema, NoteSchema, FolderShareSchema
from ..routes.rate_limiting import api_key_rate_limit_required
from ..routes.admin_helpers import audit_log
from ..routes.webhooks import trigger_otp_shared_webhook
from sqlalchemy.orm import aliased

otp_bp = Blueprint("otp", __name__)

otp_schema = OTPSchema()
otps_schema = OTPSchema(many=True)
otp_share_schema = OTPShareSchema()
otp_shares_schema = OTPShareSchema(many=True)
folder_schema = FolderSchema()
folders_schema = FolderSchema(many=True)
note_schema = NoteSchema()
notes_schema = NoteSchema(many=True)
folder_share_schema = FolderShareSchema()
folder_shares_schema = FolderShareSchema(many=True)


def get_current_user() -> User:
    """Get the current authenticated user."""
    # The user is now set on the request context `g` by the auth decorator
    if hasattr(g, "current_user") and g.current_user:
        return g.current_user
    return None


def check_otp_access(otp: OTP, user: User, required_permission: str = "read") -> bool:
    """Check if user has access to an OTP with the required permission."""
    # Owner has full access
    if otp.owner_id == user.id:
        return True
    
    # Team member access
    if otp.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=otp.team_id, user_id=user.id
        ).first()
        if team_member:
            if required_permission == "read":
                return True
            elif required_permission == "write" and team_member.role in ["admin", "member"]:
                return True
            elif required_permission == "delete" and team_member.role == "admin":
                return True
    
    # Shared access
    share = OTPShare.query.filter_by(otp_id=otp.id, shared_with_user_id=user.id).first()
    if share and share.is_active:
        if required_permission == "read":
            return True
        elif required_permission == "write" and share.permission in ["write", "admin"]:
            return True
        elif required_permission == "delete" and share.permission == "admin":
            return True
    
    return False


# -----------------------------------------------------------------------------
# OTP CRUD Operations
# -----------------------------------------------------------------------------

@otp_bp.route("/", methods=["POST"])
@audit_log("Created OTP")
@api_key_rate_limit_required(endpoint="create_otp")
def create_otp():
    """Create a new OTP entry."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    # Validate required fields
    if not data.get("name") or not data.get("secret_encrypted"):
        return jsonify({"msg": "Name and encrypted secret are required"}), HTTPStatus.BAD_REQUEST
    
    # Check if team_id is provided and user has access
    team_id = data.get("team_id")
    if team_id:
        team_member = TeamMember.query.filter_by(
            team_id=team_id, user_id=current_user.id
        ).first()
        if not team_member or team_member.role == "read-only":
            return jsonify({"msg": "Insufficient permissions for this team"}), HTTPStatus.FORBIDDEN
    
    otp = OTP(
        name=data["name"],
        secret_encrypted=data["secret_encrypted"],
        algorithm=data.get("algorithm", "SHA1"),
        digits=data.get("digits", 6),
        period=data.get("period", 30),
        counter=data.get("counter"),
        metadata=data.get("metadata", {}),
        owner_id=current_user.id if not team_id else None,
        team_id=team_id,
        folder_id=data.get("folder_id")
    )
    
    db.session.add(otp)
    db.session.commit()
    
    return otp_schema.jsonify(otp), HTTPStatus.CREATED


@otp_bp.route("/", methods=["GET"])
@api_key_rate_limit_required(endpoint="list_otps")
def list_otps():
    """List OTPs accessible to the current user, including from shared folders."""
    current_user = get_current_user()
    folder_id = request.args.get("folder_id", type=int)
    team_id = request.args.get("team_id", type=int)

    # 1. User's own OTPs
    user_otp_ids = db.session.query(OTP.id).filter_by(owner_id=current_user.id)

    # 2. Team OTPs where user is a member
    team_otp_ids = db.session.query(OTP.id).join(Team).join(TeamMember).filter(
        TeamMember.user_id == current_user.id
    )

    # 3. OTPs directly shared with the user
    shared_otp_ids = db.session.query(OTP.id).join(OTPShare).filter(
        OTPShare.shared_with_user_id == current_user.id,
        OTPShare.is_active == True
    )

    # 4. OTPs in shared folders (recursively)
    user_team_ids_query = db.session.query(TeamMember.team_id).filter(
        TeamMember.user_id == current_user.id
    )
    shared_folders_to_user = db.session.query(FolderShare.folder_id).filter(
        FolderShare.shared_with_user_id == current_user.id,
        FolderShare.is_active == True,
    )
    shared_folders_to_team = db.session.query(FolderShare.folder_id).filter(
        FolderShare.shared_with_team_id.in_(user_team_ids_query),
        FolderShare.is_active == True,
    )
    directly_shared_folder_ids_query = shared_folders_to_user.union(shared_folders_to_team)

    # Use a subquery to check for existence to avoid running a full query if not needed
    has_shared_folders = db.session.query(directly_shared_folder_ids_query.exists()).scalar()

    if has_shared_folders:
        folder_hierarchy_cte = (
            db.session.query(Folder.id)
            .filter(Folder.id.in_(directly_shared_folder_ids_query))
            .cte(name="folder_hierarchy", recursive=True)
        )
        folder_alias = aliased(Folder)
        folder_hierarchy_cte = folder_hierarchy_cte.union_all(
            db.session.query(folder_alias.id).join(
                folder_hierarchy_cte, folder_alias.parent_id == folder_hierarchy_cte.c.id
            )
        )
        accessible_shared_folder_ids = [
            fid[0] for fid in db.session.query(folder_hierarchy_cte).all()
        ]
        otps_in_shared_folders_ids = db.session.query(OTP.id).filter(OTP.folder_id.in_(accessible_shared_folder_ids))
        all_otp_ids_query = user_otp_ids.union(team_otp_ids, shared_otp_ids, otps_in_shared_folders_ids)
    else:
        all_otp_ids_query = user_otp_ids.union(team_otp_ids, shared_otp_ids)

    # Final query for OTP objects
    query = OTP.query.filter(OTP.id.in_(all_otp_ids_query))

    # Apply filters
    if folder_id:
        query = query.filter(OTP.folder_id == folder_id)
    if team_id:
        query = query.filter(OTP.team_id == team_id)

    otps = query.all()
    return otps_schema.jsonify(otps)


@otp_bp.route("/<int:otp_id>", methods=["GET"])
@api_key_rate_limit_required(endpoint="get_otp")
def get_otp(otp_id: int):
    """Get a specific OTP entry."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "read"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    return otp_schema.jsonify(otp)


@otp_bp.route("/<int:otp_id>", methods=["PUT"])
@audit_log("Updated OTP")
@api_key_rate_limit_required(endpoint="update_otp")
def update_otp(otp_id: int):
    """Update an OTP entry."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "write"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    
    # Update allowed fields
    if "name" in data:
        otp.name = data["name"]
    if "secret_encrypted" in data:
        otp.secret_encrypted = data["secret_encrypted"]
    if "algorithm" in data:
        otp.algorithm = data["algorithm"]
    if "digits" in data:
        otp.digits = data["digits"]
    if "period" in data:
        otp.period = data["period"]
    if "counter" in data:
        otp.counter = data["counter"]
    if "metadata" in data:
        otp.metadata = data["metadata"]
    if "folder_id" in data:
        otp.folder_id = data["folder_id"]
    
    db.session.commit()
    return otp_schema.jsonify(otp)


@otp_bp.route("/<int:otp_id>", methods=["DELETE"])
@audit_log("Deleted OTP")
@api_key_rate_limit_required(endpoint="delete_otp")
def delete_otp(otp_id: int):
    """Delete an OTP entry."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "delete"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    db.session.delete(otp)
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


# -----------------------------------------------------------------------------
# OTP Sharing & Collaboration
# -----------------------------------------------------------------------------

@otp_bp.route("/<int:otp_id>/share", methods=["POST"])
@audit_log("Shared OTP")
@api_key_rate_limit_required(endpoint="share_otp")
def share_otp(otp_id: int):
    """Share an OTP with another user or create a public link."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "write"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    share_type = data.get("type", "user")  # "user", "team", or "public"
    
    if share_type == "user":
        shared_with_email = data.get("email")
        shared_with_user = User.query.filter_by(email=shared_with_email).first()
        if not shared_with_user:
            return jsonify({"msg": "User not found"}), HTTPStatus.NOT_FOUND
        
        # Check if already shared
        existing_share = OTPShare.query.filter_by(
            otp_id=otp_id, shared_with_user_id=shared_with_user.id
        ).first()
        if existing_share:
            return jsonify({"msg": "Already shared with this user"}), HTTPStatus.CONFLICT
        
        share = OTPShare(
            otp_id=otp_id,
            shared_by_user_id=current_user.id,
            shared_with_user_id=shared_with_user.id,
            permission=data.get("permission", "read"),
            expires_at=data.get("expires_at")
        )
    
    elif share_type == "public":
        share = OTPShare(
            otp_id=otp_id,
            shared_by_user_id=current_user.id,
            share_type="public",
            permission=data.get("permission", "read"),
            expires_at=data.get("expires_at"),
            password_hash=data.get("password_hash"),  # Optional password protection
            access_limit=data.get("access_limit"),  # Optional access limit
            share_token=secrets.token_urlsafe(32)  # Generate secure token
        )
    
    else:
        return jsonify({"msg": "Invalid share type"}), HTTPStatus.BAD_REQUEST
    
    db.session.add(share)
    db.session.commit()
    
    if share.shared_with_user_id:
        trigger_otp_shared_webhook(
            otp_id=otp_id,
            shared_by_user_id=current_user.id,
            shared_with_user_id=share.shared_with_user_id,
        )
    
    return otp_share_schema.jsonify(share), HTTPStatus.CREATED


@otp_bp.route("/<int:otp_id>/shares", methods=["GET"])
@api_key_rate_limit_required(endpoint="list_otp_shares")
def list_otp_shares(otp_id: int):
    """List all shares for an OTP."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "read"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    shares = OTPShare.query.filter_by(otp_id=otp_id, is_active=True).all()
    return otp_shares_schema.jsonify(shares)


@otp_bp.route("/shares/<int:share_id>", methods=["DELETE"])
@audit_log("Revoked OTP share")
@api_key_rate_limit_required(endpoint="revoke_otp_share")
def revoke_otp_share(share_id: int):
    """Revoke an OTP share."""
    current_user = get_current_user()
    share = OTPShare.query.get_or_404(share_id)
    
    # Only the sharer or OTP owner can revoke
    if share.shared_by_user_id != current_user.id:
        otp = OTP.query.get(share.otp_id)
        if not check_otp_access(otp, current_user, "write"):
            return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    share.is_active = False
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


# -----------------------------------------------------------------------------
# Folder Management
# -----------------------------------------------------------------------------

@otp_bp.route("/folders", methods=["POST"])
@audit_log("Created folder")
@api_key_rate_limit_required(endpoint="create_folder")
def create_folder():
    """Create a new folder for organizing OTPs."""
    current_user = get_current_user()
    data = request.get_json() or {}
    
    if not data.get("name"):
        return jsonify({"msg": "Folder name is required"}), HTTPStatus.BAD_REQUEST
    
    team_id = data.get("team_id")
    if team_id:
        team_member = TeamMember.query.filter_by(
            team_id=team_id, user_id=current_user.id
        ).first()
        if not team_member or team_member.role == "read-only":
            return jsonify({"msg": "Insufficient permissions for this team"}), HTTPStatus.FORBIDDEN
    
    folder = Folder(
        name=data["name"],
        description=data.get("description"),
        owner_id=current_user.id if not team_id else None,
        team_id=team_id,
        parent_id=data.get("parent_id")
    )
    
    db.session.add(folder)
    db.session.commit()
    
    return folder_schema.jsonify(folder), HTTPStatus.CREATED


@otp_bp.route("/folders", methods=["GET"])
@api_key_rate_limit_required(endpoint="list_folders")
def list_folders():
    """List folders accessible to the current user."""
    current_user = get_current_user()
    team_id = request.args.get("team_id", type=int)
    parent_id = request.args.get("parent_id", type=int)
    
    query = db.session.query(Folder)
    
    # User's own folders
    user_folders = query.filter_by(owner_id=current_user.id)
    
    # Team folders where user is a member
    team_folders = query.join(Team).join(TeamMember).filter(
        TeamMember.user_id == current_user.id
    )
    
    # Combine accessible folders
    all_folders = user_folders.union(team_folders)
    
    # Apply filters
    if team_id:
        all_folders = all_folders.filter(Folder.team_id == team_id)
    if parent_id:
        all_folders = all_folders.filter(Folder.parent_id == parent_id)
    
    folders = all_folders.all()
    return folders_schema.jsonify(folders)


@otp_bp.route("/folders/<int:folder_id>", methods=["GET"])
@api_key_rate_limit_required(endpoint="get_folder")
def get_folder(folder_id: int):
    """Get a specific folder."""
    current_user = get_current_user()
    folder = Folder.query.get_or_404(folder_id)
    
    # Check access (similar to OTP access check)
    has_access = False
    if folder.owner_id == current_user.id:
        has_access = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member:
            has_access = True
    
    if not has_access:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    return folder_schema.jsonify(folder)


@otp_bp.route("/folders/<int:folder_id>", methods=["PUT"])
@audit_log("Updated folder")
@api_key_rate_limit_required(endpoint="update_folder")
def update_folder(folder_id: int):
    """Update a folder."""
    current_user = get_current_user()
    folder = Folder.query.get_or_404(folder_id)
    
    # Check write access
    has_write_access = False
    if folder.owner_id == current_user.id:
        has_write_access = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member and team_member.role in ["admin", "member"]:
            has_write_access = True
    
    if not has_write_access:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    
    if "name" in data:
        folder.name = data["name"]
    if "description" in data:
        folder.description = data["description"]
    if "parent_id" in data:
        folder.parent_id = data["parent_id"]
    
    db.session.commit()
    return folder_schema.jsonify(folder)


@otp_bp.route("/folders/<int:folder_id>", methods=["DELETE"])
@audit_log("Deleted folder")
@api_key_rate_limit_required(endpoint="delete_folder")
def delete_folder(folder_id: int):
    """Delete a folder."""
    current_user = get_current_user()
    folder = Folder.query.get_or_404(folder_id)
    
    # Check delete access (owner or team admin)
    has_delete_access = False
    if folder.owner_id == current_user.id:
        has_delete_access = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member and team_member.role == "admin":
            has_delete_access = True
    
    if not has_delete_access:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    db.session.delete(folder)
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


# -----------------------------------------------------------------------------
# Notes Management
# -----------------------------------------------------------------------------

@otp_bp.route("/<int:otp_id>/notes", methods=["POST"])
@audit_log("Added note to OTP")
@api_key_rate_limit_required(endpoint="add_note_to_otp")
def add_note_to_otp(otp_id: int):
    """Add a note to an OTP entry."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "write"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    
    if not data.get("content_encrypted"):
        return jsonify({"msg": "Encrypted content is required"}), HTTPStatus.BAD_REQUEST
    
    note = Note(
        otp_id=otp_id,
        author_id=current_user.id,
        content_encrypted=data["content_encrypted"],
        title=data.get("title", "")
    )
    
    db.session.add(note)
    db.session.commit()
    
    return note_schema.jsonify(note), HTTPStatus.CREATED


@otp_bp.route("/<int:otp_id>/notes", methods=["GET"])
@api_key_rate_limit_required(endpoint="list_otp_notes")
def list_otp_notes(otp_id: int):
    """List notes for an OTP entry."""
    current_user = get_current_user()
    otp = OTP.query.get_or_404(otp_id)
    
    if not check_otp_access(otp, current_user, "read"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    notes = Note.query.filter_by(otp_id=otp_id).all()
    return notes_schema.jsonify(notes)


@otp_bp.route("/notes/<int:note_id>", methods=["PUT"])
@audit_log("Updated note")
@api_key_rate_limit_required(endpoint="update_note")
def update_note(note_id: int):
    """Update a note."""
    current_user = get_current_user()
    note = Note.query.get_or_404(note_id)
    
    # Only the author can update their note
    if note.author_id != current_user.id:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    data = request.get_json() or {}
    
    if "content_encrypted" in data:
        note.content_encrypted = data["content_encrypted"]
    if "title" in data:
        note.title = data["title"]
    
    db.session.commit()
    return note_schema.jsonify(note)


@otp_bp.route("/notes/<int:note_id>", methods=["DELETE"])
@audit_log("Deleted note")
@api_key_rate_limit_required(endpoint="delete_note")
def delete_note(note_id: int):
    """Delete a note."""
    current_user = get_current_user()
    note = Note.query.get_or_404(note_id)
    
    # Only the author can delete their note
    if note.author_id != current_user.id:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    
    db.session.delete(note)
    db.session.commit()
    
    return "", HTTPStatus.NO_CONTENT


# -----------------------------------------------------------------------------
# Folder Sharing
# -----------------------------------------------------------------------------

@otp_bp.route("/folders/<int:folder_id>/share", methods=["POST"])
@audit_log("Shared folder")
@api_key_rate_limit_required(endpoint="share_folder")
def share_folder(folder_id: int):
    """Share a folder with another user or team."""
    current_user = get_current_user()
    folder = Folder.query.get_or_404(folder_id)

    # Check if user has admin rights on the folder
    can_share = False
    if folder.owner_id == current_user.id:
        can_share = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member and team_member.role == "admin":
            can_share = True

    if not can_share:
        return jsonify({"msg": "Only folder owners or team admins can share folders"}), HTTPStatus.FORBIDDEN

    data = request.get_json() or {}
    share_type = data.get("type", "user")
    permission = data.get("permission", "read")
    if permission not in ["read", "write", "admin"]:
        return jsonify({"msg": "Invalid permission type"}), HTTPStatus.BAD_REQUEST

    if share_type == "user":
        shared_with_email = data.get("email")
        if not shared_with_email:
            return jsonify({"msg": "Email is required for user share"}), HTTPStatus.BAD_REQUEST
        shared_with_user = User.query.filter_by(email=shared_with_email).first()
        if not shared_with_user:
            return jsonify({"msg": "User not found"}), HTTPStatus.NOT_FOUND

        existing_share = FolderShare.query.filter_by(
            folder_id=folder_id, shared_with_user_id=shared_with_user.id
        ).first()
        if existing_share:
            return jsonify({"msg": "Folder already shared with this user"}), HTTPStatus.CONFLICT

        share = FolderShare(
            folder_id=folder_id,
            shared_by_user_id=current_user.id,
            shared_with_user_id=shared_with_user.id,
            permission=permission,
            expires_at=data.get("expires_at")
        )
    elif share_type == "team":
        shared_with_team_id = data.get("team_id")
        if not shared_with_team_id:
            return jsonify({"msg": "team_id is required for team share"}), HTTPStatus.BAD_REQUEST
        team = Team.query.get(shared_with_team_id)
        if not team:
            return jsonify({"msg": "Team not found"}), HTTPStatus.NOT_FOUND

        existing_share = FolderShare.query.filter_by(
            folder_id=folder_id, shared_with_team_id=shared_with_team_id
        ).first()
        if existing_share:
            return jsonify({"msg": "Folder already shared with this team"}), HTTPStatus.CONFLICT

        share = FolderShare(
            folder_id=folder_id,
            shared_by_user_id=current_user.id,
            shared_with_team_id=shared_with_team_id,
            permission=permission,
            expires_at=data.get("expires_at")
        )
    else:
        return jsonify({"msg": "Invalid share type. Must be 'user' or 'team'."}), HTTPStatus.BAD_REQUEST

    db.session.add(share)
    db.session.commit()

    return folder_share_schema.jsonify(share), HTTPStatus.CREATED


@otp_bp.route("/folders/<int:folder_id>/shares", methods=["GET"])
@api_key_rate_limit_required(endpoint="list_folder_shares")
def list_folder_shares(folder_id: int):
    """List all shares for a folder."""
    current_user = get_current_user()
    folder = Folder.query.get_or_404(folder_id)

    # Check read access to folder
    has_read_access = False
    if folder.owner_id == current_user.id:
        has_read_access = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member:
            has_read_access = True

    if not has_read_access:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN

    shares = FolderShare.query.filter_by(folder_id=folder_id, is_active=True).all()
    return folder_shares_schema.jsonify(shares)


@otp_bp.route("/shares/folder/<int:share_id>", methods=["DELETE"])
@audit_log("Revoked folder share")
@api_key_rate_limit_required(endpoint="revoke_folder_share")
def revoke_folder_share(share_id: int):
    """Revoke a folder share."""
    current_user = get_current_user()
    share = FolderShare.query.get_or_404(share_id)
    folder = Folder.query.get_or_404(share.folder_id)

    # Check if user has admin rights on the folder or is the sharer
    can_revoke = False
    if share.shared_by_user_id == current_user.id:
        can_revoke = True
    elif folder.owner_id == current_user.id:
        can_revoke = True
    elif folder.team_id:
        team_member = TeamMember.query.filter_by(
            team_id=folder.team_id, user_id=current_user.id
        ).first()
        if team_member and team_member.role == "admin":
            can_revoke = True

    if not can_revoke:
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN

    share.is_active = False
    db.session.commit()

    return "", HTTPStatus.NO_CONTENT
