"""Custom Flask CLI commands for database management and admin tasks."""
from __future__ import annotations

import click
import secrets
from datetime import datetime, timedelta
from flask.cli import with_appcontext

from .extensions import bcrypt, db
from .models import User, Role, Team, Coupon, Invitation, SystemSetting


@click.command("create-admin")
@click.argument("email")
@click.password_option()
@click.option("--role", default="Super Admin", help="Admin role to assign")
@with_appcontext
def create_admin(email: str, password: str, role: str):
    """Create an admin user from CLI."""
    if User.query.filter_by(email=email).first():
        click.echo(f"User with email {email} already exists.")
        return
    
    # Create or get the role
    admin_role = Role.query.filter_by(name=role).first()
    if not admin_role:
        admin_role = Role(name=role, description=f"{role} role")
        db.session.add(admin_role)
    
    pw_hash = bcrypt.generate_password_hash(password).decode()
    admin = User(
        email=email, 
        password_hash=pw_hash,
        credits=1000,  # Give admin some credits
        is_active=True
    )
    admin.roles.append(admin_role)
    
    db.session.add(admin)
    db.session.commit()
    click.echo(f"Admin user {email} created with role {role}.")


@click.command("init-db")
@with_appcontext
def init_database():
    """Initialize the database with default data."""
    click.echo("Initializing database...")
    
    # Create all tables
    db.create_all()
    
    # Create default roles
    roles = [
        ("Super Admin", "Full platform control and management"),
        ("Developer Admin", "Technical integrations and API management"),
        ("Sales", "Sales account management and coupon creation"),
        ("Sales Admin", "Sales admin (alias for Sales)"),
        ("Marketing", "Marketing campaigns and invitation management"),
        ("Marketing Admin", "Marketing admin (alias for Marketing)"),
        ("Support", "Customer support and ticket management"),
    ]
    
    for role_name, description in roles:
        if not Role.query.filter_by(name=role_name).first():
            role = Role(name=role_name, description=description)
            db.session.add(role)
    
    # Create default system settings
    default_settings = [
        ("platform_name", "Cloud OTP"),
        ("default_user_credits", 100),
        ("default_team_credits", 500),
        ("max_otp_per_user", 1000),
        ("max_teams_per_user", 10),
        ("webhook_enabled", True),
        ("analytics_enabled", True),
        ("maintenance_mode", False),
    ]
    
    for key, value in default_settings:
        if not SystemSetting.query.get(key):
            setting = SystemSetting(key=key, value=value)
            db.session.add(setting)
    
    db.session.commit()
    click.echo("Database initialized successfully.")


@click.command("reset-db")
@click.confirmation_option(prompt="Are you sure you want to reset the database?")
@with_appcontext
def reset_database():
    """Reset the database (WARNING: This will delete all data)."""
    click.echo("Resetting database...")
    db.drop_all()
    db.create_all()
    click.echo("Database reset completed.")


@click.command("create-test-data")
@click.option("--users", default=10, help="Number of test users to create")
@click.option("--teams", default=3, help="Number of test teams to create")
@with_appcontext
def create_test_data(users: int, teams: int):
    """Create test data for development."""
    click.echo(f"Creating {users} test users and {teams} test teams...")
    
    # Create test users
    for i in range(users):
        email = f"user{i+1}@example.com"
        if not User.query.filter_by(email=email).first():
            pw_hash = bcrypt.generate_password_hash("password123").decode()
            user = User(
                email=email,
                first_name=f"User",
                last_name=f"{i+1}",
                password_hash=pw_hash,
                credits=100,
                is_active=True
            )
            db.session.add(user)
    
    db.session.commit()
    
    # Create test teams
    users_list = User.query.limit(users).all()
    for i in range(teams):
        team_name = f"Test Team {i+1}"
        if not Team.query.filter_by(name=team_name).first():
            owner = users_list[i % len(users_list)]
            team = Team(
                name=team_name,
                owner=owner,
                credits=500
            )
            db.session.add(team)
    
    db.session.commit()
    click.echo("Test data created successfully.")


@click.command("create-coupon")
@click.argument("code")
@click.argument("discount_type", type=click.Choice(["percentage", "fixed_amount", "credits"]))
@click.argument("value", type=int)
@click.option("--expires-days", default=30, help="Days until expiration")
@click.option("--max-uses", default=None, type=int, help="Maximum number of uses")
@with_appcontext
def create_coupon(code: str, discount_type: str, value: int, expires_days: int, max_uses: int | None):
    """Create a coupon code."""
    if Coupon.query.filter_by(code=code).first():
        click.echo(f"Coupon with code {code} already exists.")
        return
    
    expires_at = datetime.utcnow() + timedelta(days=expires_days)
    
    coupon = Coupon(
        code=code,
        discount_type=discount_type,
        value=value,
        expires_at=expires_at,
        max_uses=max_uses,
        is_active=True
    )
    
    db.session.add(coupon)
    db.session.commit()
    
    click.echo(f"Coupon {code} created: {value} {discount_type}, expires in {expires_days} days.")


@click.command("create-invitation")
@click.argument("created_by_email")
@click.option("--team-name", default=None, help="Team name for team invitation")
@click.option("--expires-days", default=7, help="Days until expiration")
@with_appcontext
def create_invitation(created_by_email: str, team_name: str | None, expires_days: int):
    """Create an invitation code."""
    creator = User.query.filter_by(email=created_by_email).first()
    if not creator:
        click.echo(f"User {created_by_email} not found.")
        return
    
    team = None
    if team_name:
        team = Team.query.filter_by(name=team_name).first()
        if not team:
            click.echo(f"Team {team_name} not found.")
            return
    
    code = f"inv_{secrets.token_urlsafe(16)}"
    expires_at = datetime.utcnow() + timedelta(days=expires_days)
    
    invitation = Invitation(
        code=code,
        created_by=creator,
        team=team,
        expires_at=expires_at,
        is_active=True
    )
    
    db.session.add(invitation)
    db.session.commit()
    
    invitation_type = "team" if team else "platform"
    click.echo(f"Invitation code created: {code} ({invitation_type} invitation, expires in {expires_days} days)")


@click.command("list-users")
@click.option("--active-only", is_flag=True, help="Show only active users")
@click.option("--admins-only", is_flag=True, help="Show only admin users")
@with_appcontext
def list_users(active_only: bool, admins_only: bool):
    """List all users."""
    query = User.query
    
    if active_only:
        query = query.filter_by(is_active=True)
    
    if admins_only:
        query = query.join(User.roles).filter(Role.name.in_(["Super Admin", "Developer Admin", "Sales", "Marketing"]))
    
    users = query.all()
    
    click.echo(f"Found {len(users)} users:")
    for user in users:
        roles = ", ".join([role.name for role in user.roles])
        status = "Active" if user.is_active else "Inactive"
        click.echo(f"  {user.email} - {status} - Credits: {user.credits} - Roles: {roles or 'None'}")


@click.command("user-info")
@click.argument("email")
@with_appcontext
def user_info(email: str):
    """Get detailed information about a user."""
    user = User.query.filter_by(email=email).first()
    if not user:
        click.echo(f"User {email} not found.")
        return
    
    click.echo(f"User Information for {email}:")
    click.echo(f"  ID: {user.id}")
    click.echo(f"  Name: {user.first_name} {user.last_name}")
    click.echo(f"  Status: {'Active' if user.is_active else 'Inactive'}")
    click.echo(f"  Credits: {user.credits}")
    click.echo(f"  Created: {user.created_at}")
    click.echo(f"  Roles: {', '.join([role.name for role in user.roles]) or 'None'}")
    click.echo(f"  OTPs: {len(user.otps)}")
    click.echo(f"  API Keys: {len([k for k in user.api_keys if k.is_active])}")
    click.echo(f"  Teams: {len(user.teams)}")
    click.echo(f"  Has Secondary Password: {'Yes' if user.secondary_password_hash else 'No'}")


@click.command("add-credits")
@click.argument("email")
@click.argument("amount", type=int)
@with_appcontext
def add_credits(email: str, amount: int):
    """Add credits to a user account."""
    user = User.query.filter_by(email=email).first()
    if not user:
        click.echo(f"User {email} not found.")
        return
    
    old_credits = user.credits
    user.credits += amount
    db.session.commit()
    
    click.echo(f"Added {amount} credits to {email}. Balance: {old_credits} -> {user.credits}")


@click.command("system-stats")
@with_appcontext
def system_stats():
    """Display system statistics."""
    from .models import OTP, Team, APIKey, Ticket, Payment, Coupon, Invitation
    
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    total_teams = Team.query.count()
    total_otps = OTP.query.count()
    active_api_keys = APIKey.query.filter_by(is_active=True).count()
    open_tickets = Ticket.query.filter_by(status="open").count()
    total_payments = Payment.query.count()
    active_coupons = Coupon.query.filter_by(is_active=True).count()
    active_invitations = Invitation.query.filter_by(is_active=True).count()
    
    # Calculate total credits
    total_user_credits = db.session.query(db.func.sum(User.credits)).scalar() or 0
    total_team_credits = db.session.query(db.func.sum(Team.credits)).scalar() or 0
    
    click.echo("System Statistics:")
    click.echo(f"  Users: {active_users}/{total_users} (active/total)")
    click.echo(f"  Teams: {total_teams}")
    click.echo(f"  OTPs: {total_otps}")
    click.echo(f"  Active API Keys: {active_api_keys}")
    click.echo(f"  Open Support Tickets: {open_tickets}")
    click.echo(f"  Total Payments: {total_payments}")
    click.echo(f"  Active Coupons: {active_coupons}")
    click.echo(f"  Active Invitations: {active_invitations}")
    click.echo(f"  Total Credits: {total_user_credits + total_team_credits} (Users: {total_user_credits}, Teams: {total_team_credits})")


@click.command("cleanup-expired")
@with_appcontext
def cleanup_expired():
    """Clean up expired invitations and coupons."""
    now = datetime.utcnow()
    
    # Deactivate expired invitations
    expired_invitations = Invitation.query.filter(
        Invitation.expires_at < now,
        Invitation.is_active == True
    ).all()
    
    for invitation in expired_invitations:
        invitation.is_active = False
    
    # Deactivate expired coupons
    expired_coupons = Coupon.query.filter(
        Coupon.expires_at < now,
        Coupon.is_active == True
    ).all()
    
    for coupon in expired_coupons:
        coupon.is_active = False
    
    db.session.commit()
    
    click.echo(f"Cleaned up {len(expired_invitations)} expired invitations and {len(expired_coupons)} expired coupons.")


# Register all CLI commands
cli_commands = [
    create_admin,
    init_database,
    reset_database,
    create_test_data,
    create_coupon,
    create_invitation,
    list_users,
    user_info,
    add_credits,
    system_stats,
    cleanup_expired,
]
