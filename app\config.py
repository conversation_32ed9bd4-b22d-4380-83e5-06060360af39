"""Configuration classes for Cloud OTP Flask application."""
from __future__ import annotations

import os
from datetime import timedelta

from dotenv import load_dotenv

load_dotenv()


class Config:  # pylint: disable=too-few-public-methods
    """Base configuration (production sensible defaults)."""

    # General ----------------------------------------------------------------
    SECRET_KEY: str = os.getenv("SECRET_KEY", os.urandom(32).hex())
    FLASK_ENV: str = os.getenv("FLASK_ENV", "production")
    DEBUG: bool = FLASK_ENV == "development"

    # Database ---------------------------------------------------------------
    SQLALCHEMY_DATABASE_URI: str = os.getenv("DATABASE_URL", "sqlite:///cloud_otp.db")
    SQLALCHEMY_TRACK_MODIFICATIONS: bool = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_pre_ping": True,
        "pool_recycle": 300,
    }

    # JWT --------------------------------------------------------------------
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", SECRET_KEY)
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES: timedelta = timedelta(days=30)

    # OAuth / Social Logins --------------------------------------------------
    GOOGLE_CLIENT_ID: str = os.getenv("GOOGLE_CLIENT_ID", "")
    GOOGLE_CLIENT_SECRET: str = os.getenv("GOOGLE_CLIENT_SECRET", "")
    GITHUB_CLIENT_ID: str = os.getenv("GITHUB_CLIENT_ID", "")
    GITHUB_CLIENT_SECRET: str = os.getenv("GITHUB_CLIENT_SECRET", "")
    MICROSOFT_CLIENT_ID: str = os.getenv("MICROSOFT_CLIENT_ID", "")
    MICROSOFT_CLIENT_SECRET: str = os.getenv("MICROSOFT_CLIENT_SECRET", "")
    LINKEDIN_CLIENT_ID: str = os.getenv("LINKEDIN_CLIENT_ID", "")
    LINKEDIN_CLIENT_SECRET: str = os.getenv("LINKEDIN_CLIENT_SECRET", "")
    FACEBOOK_CLIENT_ID: str = os.getenv("FACEBOOK_CLIENT_ID", "")
    FACEBOOK_CLIENT_SECRET: str = os.getenv("FACEBOOK_CLIENT_SECRET", "")

    # SAML/OIDC SSO ----------------------------------------------------------
    SAML_METADATA_URL: str = os.getenv("SAML_METADATA_URL", "")
    OIDC_DISCOVERY_URL: str = os.getenv("OIDC_DISCOVERY_URL", "")
    OIDC_CLIENT_ID: str = os.getenv("OIDC_CLIENT_ID", "")
    OIDC_CLIENT_SECRET: str = os.getenv("OIDC_CLIENT_SECRET", "")

    # Payment Processing (Paddle) -------------------------------------------
    PADDLE_VENDOR_ID: str = os.getenv("PADDLE_VENDOR_ID", "")
    PADDLE_API_KEY: str = os.getenv("PADDLE_API_KEY", "")
    PADDLE_PUBLIC_KEY: str = os.getenv("PADDLE_PUBLIC_KEY", "")
    PADDLE_WEBHOOK_SECRET: str = os.getenv("PADDLE_WEBHOOK_SECRET", "")
    PADDLE_SANDBOX: bool = os.getenv("PADDLE_SANDBOX", "true").lower() == "true"

    # Rate Limiting & API Quotas --------------------------------------------
    DEFAULT_RATE_LIMIT_PER_SECOND: int = int(os.getenv("DEFAULT_RATE_LIMIT_PER_SECOND", "10"))
    DEFAULT_API_QUOTA_PER_MONTH: int = int(os.getenv("DEFAULT_API_QUOTA_PER_MONTH", "1000"))
    RATE_LIMIT_STORAGE_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # Email Configuration ---------------------------------------------------
    MAIL_SERVER: str = os.getenv("MAIL_SERVER", "")
    MAIL_PORT: int = int(os.getenv("MAIL_PORT", "587"))
    MAIL_USE_TLS: bool = os.getenv("MAIL_USE_TLS", "true").lower() == "true"
    MAIL_USERNAME: str = os.getenv("MAIL_USERNAME", "")
    MAIL_PASSWORD: str = os.getenv("MAIL_PASSWORD", "")
    MAIL_DEFAULT_SENDER: str = os.getenv("MAIL_DEFAULT_SENDER", "<EMAIL>")

    # Security ---------------------------------------------------------------
    SESSION_COOKIE_SECURE: bool = True
    SESSION_COOKIE_HTTPONLY: bool = True
    SESSION_COOKIE_SAMESITE: str = "Lax"
    PERMANENT_SESSION_LIFETIME: timedelta = timedelta(hours=24)

    # CORS -------------------------------------------------------------------
    CORS_ORIGINS: list[str] = os.getenv("CORS_ORIGINS", "*").split(",")

    # Webhook Configuration --------------------------------------------------
    WEBHOOK_TIMEOUT: int = int(os.getenv("WEBHOOK_TIMEOUT", "30"))
    WEBHOOK_MAX_RETRIES: int = int(os.getenv("WEBHOOK_MAX_RETRIES", "5"))
    WEBHOOK_RETRY_DELAY: int = int(os.getenv("WEBHOOK_RETRY_DELAY", "300"))  # 5 minutes

    # File Upload ------------------------------------------------------------
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER: str = os.getenv("UPLOAD_FOLDER", "uploads")

    # Logging ----------------------------------------------------------------
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "cloud_otp.log")

    # Monitoring & Analytics -------------------------------------------------
    SENTRY_DSN: str = os.getenv("SENTRY_DSN", "")
    ANALYTICS_ENABLED: bool = os.getenv("ANALYTICS_ENABLED", "true").lower() == "true"

    # Feature Flags ----------------------------------------------------------
    FEATURE_SOCIAL_LOGIN: bool = os.getenv("FEATURE_SOCIAL_LOGIN", "true").lower() == "true"
    FEATURE_SSO: bool = os.getenv("FEATURE_SSO", "true").lower() == "true"
    FEATURE_WEBHOOKS: bool = os.getenv("FEATURE_WEBHOOKS", "true").lower() == "true"
    FEATURE_ANALYTICS: bool = os.getenv("FEATURE_ANALYTICS", "true").lower() == "true"
    FEATURE_RATE_LIMITING: bool = os.getenv("FEATURE_RATE_LIMITING", "true").lower() == "true"

    # Encryption -------------------------------------------------------------
    ENCRYPTION_KEY: str = os.getenv("ENCRYPTION_KEY", "")  # For additional server-side encryption
    
    # Default User Settings --------------------------------------------------
    DEFAULT_USER_CREDITS: int = int(os.getenv("DEFAULT_USER_CREDITS", "100"))
    DEFAULT_TEAM_CREDITS: int = int(os.getenv("DEFAULT_TEAM_CREDITS", "500"))

    # Redis ---------------------------------------------------------------
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_SESSION_DB: int = int(os.getenv("REDIS_SESSION_DB", "1"))
    REDIS_CACHE_DB: int = int(os.getenv("REDIS_CACHE_DB", "2"))

    # Celery --------------------------------------------------------------
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/1")
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/2")
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: list[str] = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True

    # Monitoring & Error Tracking -----------------------------------------
    SENTRY_ENVIRONMENT: str = os.getenv("SENTRY_ENVIRONMENT", "production")
    SENTRY_TRACES_SAMPLE_RATE: float = float(os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.1"))
    PROMETHEUS_MULTIPROC_DIR: str = os.getenv("PROMETHEUS_MULTIPROC_DIR", "/tmp")


class DevelopmentConfig(Config):  # pylint: disable=too-few-public-methods
    """Development configuration."""

    DEBUG: bool = True
    SQLALCHEMY_DATABASE_URI: str = os.getenv("DEV_DATABASE_URL", "sqlite:///cloud_otp_dev.db")
    
    # Relaxed security for development
    SESSION_COOKIE_SECURE: bool = False
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(hours=24)  # Longer for development
    
    # Enable all features in development
    FEATURE_SOCIAL_LOGIN: bool = True
    FEATURE_SSO: bool = True
    FEATURE_WEBHOOKS: bool = True
    FEATURE_ANALYTICS: bool = True
    FEATURE_RATE_LIMITING: bool = True


class TestingConfig(Config):  # pylint: disable=too-few-public-methods
    """Testing configuration."""

    TESTING: bool = True
    SQLALCHEMY_DATABASE_URI: str = "sqlite:///:memory:"
    DEBUG: bool = True
    WTF_CSRF_ENABLED: bool = False
    
    # Disable external services in testing
    FEATURE_WEBHOOKS: bool = False
    FEATURE_ANALYTICS: bool = False
    
    # Fast token expiry for testing
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(minutes=5)
    
    # Disable rate limiting in tests
    FEATURE_RATE_LIMITING: bool = False


class ProductionConfig(Config):  # pylint: disable=too-few-public-methods
    """Production configuration."""

    DEBUG: bool = False
    
    # Strict security settings
    SESSION_COOKIE_SECURE: bool = True
    SESSION_COOKIE_HTTPONLY: bool = True
    SESSION_COOKIE_SAMESITE: str = "Strict"
    
    # Production database with connection pooling
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_size": 10,
        "pool_recycle": 120,
        "pool_pre_ping": True,
        "max_overflow": 20,
    }
    
    # Shorter token expiry for security
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(minutes=30)


class StagingConfig(ProductionConfig):  # pylint: disable=too-few-public-methods
    """Staging configuration (production-like but with some debugging)."""

    DEBUG: bool = True
    SQLALCHEMY_DATABASE_URI: str = os.getenv("STAGING_DATABASE_URL", "sqlite:///cloud_otp_staging.db")


# Configuration mapping
config_map = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "staging": StagingConfig,
    "production": ProductionConfig,
}


def get_config(config_name: str | None = None) -> type[Config]:
    """Get configuration class based on environment."""
    if config_name is None:
        config_name = os.getenv("FLASK_ENV", "production")
    
    return config_map.get(config_name, ProductionConfig)
