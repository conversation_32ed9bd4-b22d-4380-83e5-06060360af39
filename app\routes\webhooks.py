"""Webhook management and delivery system."""
from __future__ import annotations

from http import HTTPStatus
from datetime import datetime, timedelta
import json
import requests
from typing import Any

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import WebhookEvent, User, WebhookConfig, OTP, Team, TeamMember
from ..schemas import WebhookEventSchema, WebhookConfigSchema
from ..routes.admin_helpers import admin_required, audit_log
from ..tasks import deliver_webhook

webhooks_bp = Blueprint("webhooks", __name__)

webhook_event_schema = WebhookEventSchema()
webhook_events_schema = WebhookEventSchema(many=True)
webhook_config_schema = WebhookConfigSchema()
webhook_configs_schema = WebhookConfigSchema(many=True)


def get_current_user() -> User:
    """Get the current authenticated user."""
    user_id: int = get_jwt_identity()  # type: ignore
    return User.query.get_or_404(user_id)


def send_webhook(event_type: str, payload: dict[str, Any], target_urls: list[str]) -> None:
    """Queue webhook events for delivery."""
    events_to_send = []
    for url in target_urls:
        webhook_event = WebhookEvent(
            event_type=event_type,
            payload=payload,
            target_url=url,
            status="pending",
        )
        db.session.add(webhook_event)
        events_to_send.append(webhook_event)
    db.session.commit()
    for event in events_to_send:
        deliver_webhook.delay(event.id)


def deliver_webhook(webhook_event: WebhookEvent) -> bool:
    """Attempt to deliver a webhook event."""
    try:
        webhook_event.attempts += 1
        webhook_event.last_attempt_at = datetime.utcnow()
        
        # Prepare webhook payload
        webhook_payload = {
            "event_type": webhook_event.event_type,
            "timestamp": webhook_event.created_at.isoformat() + "Z",
            "data": webhook_event.payload
        }
        
        # Send HTTP POST request
        response = requests.post(
            webhook_event.target_url,
            json=webhook_payload,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "CloudOTP-Webhook/1.0"
            },
            timeout=30
        )
        
        webhook_event.response_status = response.status_code
        webhook_event.response_body = response.text[:1000]  # Limit response body size
        
        if response.status_code == 200:
            webhook_event.status = "sent"
            db.session.commit()
            return True
        else:
            webhook_event.status = "failed"
            db.session.commit()
            return False
            
    except Exception as e:
        webhook_event.status = "failed"
        webhook_event.response_body = str(e)[:1000]
        db.session.commit()
        return False


@webhooks_bp.route("/events", methods=["GET"])
@jwt_required()
@admin_required
def list_webhook_events():
    """List webhook events (admin only)."""
    status = request.args.get("status")
    event_type = request.args.get("event_type")
    limit = min(int(request.args.get("limit", 100)), 1000)
    
    query = WebhookEvent.query
    
    if status:
        query = query.filter_by(status=status)
    if event_type:
        query = query.filter_by(event_type=event_type)
    
    events = query.order_by(WebhookEvent.created_at.desc()).limit(limit).all()
    return webhook_events_schema.jsonify(events)


@webhooks_bp.route("/events/<int:event_id>", methods=["GET"])
@jwt_required()
@admin_required
def get_webhook_event(event_id: int):
    """Get details of a specific webhook event."""
    event = WebhookEvent.query.get_or_404(event_id)
    return webhook_event_schema.jsonify(event)


@webhooks_bp.route("/events/<int:event_id>/retry", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Retried webhook delivery")
def retry_webhook_event(event_id: int):
    """Retry delivery of a failed webhook event."""
    event = WebhookEvent.query.get_or_404(event_id)
    
    if event.status == "sent":
        return jsonify({"msg": "Event already delivered successfully"}), HTTPStatus.BAD_REQUEST
    
    if event.attempts >= 5:
        return jsonify({"msg": "Maximum retry attempts reached"}), HTTPStatus.BAD_REQUEST
    
    deliver_webhook.delay(event.id)
    
    return jsonify({"msg": "Webhook delivery has been re-queued."})


@webhooks_bp.route("/test", methods=["POST"])
@jwt_required()
@admin_required
@audit_log("Sent test webhook")
def send_test_webhook():
    """Send a test webhook to verify endpoint configuration."""
    data = request.get_json() or {}
    target_url = data.get("target_url")
    
    if not target_url:
        return jsonify({"msg": "target_url is required"}), HTTPStatus.BAD_REQUEST
    
    test_payload = {
        "event_type": "test",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "data": {
            "message": "This is a test webhook from Cloud OTP",
            "test_id": f"test_{datetime.utcnow().timestamp()}"
        }
    }
    
    try:
        response = requests.post(
            target_url,
            json=test_payload,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "CloudOTP-Webhook/1.0"
            },
            timeout=30
        )
        
        return jsonify({
            "msg": "Test webhook sent",
            "status_code": response.status_code,
            "response": response.text[:500]
        })
        
    except Exception as e:
        return jsonify({
            "msg": "Test webhook failed",
            "error": str(e)
        }), HTTPStatus.INTERNAL_SERVER_ERROR


@webhooks_bp.route("/process-pending", methods=["POST"])
@jwt_required()
@admin_required
def process_pending_webhooks():
    """Process pending webhook deliveries (admin only)."""
    # Get pending webhooks (not too old to avoid infinite retries)
    cutoff_time = datetime.utcnow() - timedelta(hours=24)
    
    pending_webhooks = WebhookEvent.query.filter(
        WebhookEvent.status == "pending",
        WebhookEvent.attempts < 5,
        WebhookEvent.created_at >= cutoff_time
    ).limit(100).all()
    
    count = len(pending_webhooks)
    for webhook in pending_webhooks:
        deliver_webhook.delay(webhook.id)
    
    return jsonify({
        "msg": f"Re-queued {count} pending webhooks for processing."
    })


@webhooks_bp.route("/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_webhook_stats():
    """Get webhook delivery statistics."""
    total_events = WebhookEvent.query.count()
    pending_events = WebhookEvent.query.filter_by(status="pending").count()
    sent_events = WebhookEvent.query.filter_by(status="sent").count()
    failed_events = WebhookEvent.query.filter_by(status="failed").count()
    
    # Recent events (last 24 hours)
    recent_cutoff = datetime.utcnow() - timedelta(hours=24)
    recent_events = WebhookEvent.query.filter(
        WebhookEvent.created_at >= recent_cutoff
    ).count()
    
    # Events by type
    event_types = db.session.query(
        WebhookEvent.event_type,
        db.func.count(WebhookEvent.id).label("count")
    ).group_by(WebhookEvent.event_type).all()
    
    return jsonify({
        "total_events": total_events,
        "pending_events": pending_events,
        "sent_events": sent_events,
        "failed_events": failed_events,
        "recent_events": recent_events,
        "success_rate": round((sent_events / total_events) * 100, 2) if total_events > 0 else 0,
        "event_types": [{"type": et[0], "count": et[1]} for et in event_types]
    })


@webhooks_bp.route("/config", methods=["POST"])
@jwt_required()
@audit_log("Created webhook configuration")
def create_webhook_config():
    """Create a new webhook configuration for the user or a team."""
    current_user = get_current_user()
    data = request.get_json() or {}

    config_data = webhook_config_schema.load(data)

    team_id = data.get("team_id")
    if team_id:
        team = Team.query.get_or_404(team_id)
        # Check if user is admin of the team
        member = TeamMember.query.filter_by(team_id=team.id, user_id=current_user.id).first()
        if not (team.owner_id == current_user.id or (member and member.role == "admin")):
            return jsonify({"msg": "Only team admins can create webhook configurations for a team"}), HTTPStatus.FORBIDDEN
        config_data.team_id = team_id
        config_data.user_id = None
    else:
        config_data.user_id = current_user.id
        config_data.team_id = None

    db.session.add(config_data)
    db.session.commit()

    return webhook_config_schema.jsonify(config_data), HTTPStatus.CREATED


@webhooks_bp.route("/config", methods=["GET"])
@jwt_required()
def list_webhook_configs():
    """List webhook configurations for the user and their teams."""
    current_user = get_current_user()

    # Get user's personal configs
    user_configs = WebhookConfig.query.filter_by(user_id=current_user.id).all()

    # Get configs for teams where user is an admin or owner
    team_configs = WebhookConfig.query.join(Team).filter(
        (Team.owner_id == current_user.id) |
        (Team.members.any(TeamMember.user_id == current_user.id, TeamMember.role == 'admin'))
    ).all()

    all_configs = user_configs + team_configs
    return webhook_configs_schema.jsonify(list(set(all_configs)))


@webhooks_bp.route("/config/<int:config_id>", methods=["GET"])
@jwt_required()
def get_webhook_config(config_id: int):
    """Get a specific webhook configuration."""
    current_user = get_current_user()
    config = WebhookConfig.query.get_or_404(config_id)

    # Check access
    if config.user_id == current_user.id:
        return webhook_config_schema.jsonify(config)

    if config.team_id:
        team = Team.query.get_or_404(config.team_id)
        member = TeamMember.query.filter_by(team_id=team.id, user_id=current_user.id).first()
        if team.owner_id == current_user.id or (member and member.role in ["admin"]):
            return webhook_config_schema.jsonify(config)

    return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN


@webhooks_bp.route("/config/<int:config_id>", methods=["PUT"])
@jwt_required()
@audit_log("Updated webhook configuration")
def update_webhook_config(config_id: int):
    """Update a webhook configuration."""
    config = WebhookConfig.query.get_or_404(config_id)
    # Access check is implicitly handled by get_webhook_config logic
    get_webhook_config(config_id)

    data = request.get_json() or {}
    webhook_config_schema.load(data, partial=True)

    for key, value in data.items():
        setattr(config, key, value)

    db.session.commit()
    return webhook_config_schema.jsonify(config)


@webhooks_bp.route("/config/<int:config_id>", methods=["DELETE"])
@jwt_required()
@audit_log("Deleted webhook configuration")
def delete_webhook_config(config_id: int):
    """Delete a webhook configuration."""
    config = WebhookConfig.query.get_or_404(config_id)
    get_webhook_config(config_id)  # Access check
    db.session.delete(config)
    db.session.commit()
    return "", HTTPStatus.NO_CONTENT


def _get_target_urls_for_event(event_type: str, user_id: int | None = None, team_id: int | None = None) -> list[str]:
    """Helper to get webhook URLs for a specific event and entity."""
    if not user_id and not team_id:
        return []

    queries = []
    if user_id:
        queries.append(WebhookConfig.query.filter_by(user_id=user_id, is_active=True))
    if team_id:
        queries.append(WebhookConfig.query.filter_by(team_id=team_id, is_active=True))

    configs = []
    for q in queries:
        configs.extend(q.all())

    target_urls = []
    for config in set(configs):  # Use set to avoid duplicate configs if user and team are both present
        subscribed_events = [e.strip() for e in config.subscribed_events.split(',')]
        if "*" in subscribed_events or event_type in subscribed_events:
            target_urls.append(config.target_url)

    return target_urls


def trigger_otp_shared_webhook(otp_id: int, shared_with_user_id: int, shared_by_user_id: int) -> None:
    """Trigger webhook when an OTP is shared."""
    otp = OTP.query.get(otp_id)
    if not otp:
        return

    payload = {
        "otp_id": otp_id,
        "shared_with_user_id": shared_with_user_id,
        "shared_by_user_id": shared_by_user_id
    }
    event_type = "otp.shared"

    webhook_urls = _get_target_urls_for_event(event_type, user_id=otp.owner_id, team_id=otp.team_id)

    if webhook_urls:
        send_webhook(event_type, payload, webhook_urls)


def trigger_user_registered_webhook(user_id: int) -> None:
    """Trigger webhook when a new user registers."""
    payload = {"user_id": user_id}
    event_type = "user.registered"
    webhook_urls = _get_target_urls_for_event(event_type, user_id=user_id)
    if webhook_urls:
        send_webhook(event_type, payload, webhook_urls)


def trigger_payment_received_webhook(payment_id: int, user_id: int | None, team_id: int | None, amount: int) -> None:
    """Trigger webhook when a payment is received."""
    payload = {
        "payment_id": payment_id,
        "user_id": user_id, "team_id": team_id,
        "amount": amount
    }
    event_type = "payment.received"
    webhook_urls = _get_target_urls_for_event(event_type, user_id=user_id, team_id=team_id)
    if webhook_urls:
        send_webhook(event_type, payload, webhook_urls)


def trigger_team_member_added_webhook(team_id: int, user_id: int, role: str) -> None:
    """Trigger webhook when a team member is added."""
    payload = {
        "team_id": team_id, "user_id": user_id, "role": role
    }
    event_type = "team.member_added"
    webhook_urls = _get_target_urls_for_event(event_type, team_id=team_id)
    if webhook_urls:
        send_webhook(event_type, payload, webhook_urls)


def trigger_support_ticket_created_webhook(ticket_id: int, user_id: int, subject: str) -> None:
    """Trigger webhook when a support ticket is created."""
    payload = {
        "ticket_id": ticket_id, "user_id": user_id, "subject": subject
    }
    event_type = "support.ticket_created"
    webhook_urls = _get_target_urls_for_event(event_type, user_id=user_id)
    if webhook_urls:
        send_webhook(event_type, payload, webhook_urls)