import base64
import os
from typing import <PERSON>ple
from argon2 import Password<PERSON>asher, Type
from argon2.exceptions import VerifyMismatchError, HashingError
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.hkdf import HKDF

# Argon2id parameters following NIST guidelines and Brief requirements
ARGON2_TIME_COST = 3        # Number of iterations
ARGON2_MEMORY_COST = 65536  # Memory usage in KiB (64 MB)
ARGON2_PARALLELISM = 4      # Number of parallel threads
ARGON2_HASH_LENGTH = 32     # Output hash length in bytes
SALT_SIZE = 16              # 128 bits


def derive_key(password: str, salt: bytes) -> bytes:
    """Derive a Fernet key from a password and salt using Argon2id (military-grade)."""
    ph = PasswordHasher(
        time_cost=ARGON2_TIME_COST,
        memory_cost=ARGON2_MEMORY_COST,
        parallelism=ARGON2_PARALLELISM,
        hash_len=ARGON2_HASH_LENGTH,
        type=Type.ID  # Argon2id variant for maximum security
    )
    
    try:
        # Use low-level hash function for deterministic key derivation
        from argon2.low_level import hash_secret_raw
        
        raw_key = hash_secret_raw(
            secret=password.encode('utf-8'),
            salt=salt,
            time_cost=ARGON2_TIME_COST,
            memory_cost=ARGON2_MEMORY_COST,
            parallelism=ARGON2_PARALLELISM,
            hash_len=ARGON2_HASH_LENGTH,
            type=Type.ID
        )
        
        # Ensure we have exactly 32 bytes for Fernet
        if len(raw_key) != 32:
            # Use HKDF to expand/contract to exactly 32 bytes if needed
            hkdf = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                info=b'cloud-otp-fernet-key'
            )
            raw_key = hkdf.derive(raw_key)
            
        return base64.urlsafe_b64encode(raw_key)
        
    except (HashingError, ValueError) as e:
        raise InvalidToken(f"Key derivation failed: {str(e)}")


def encrypt_with_password(data: bytes, password: str) -> Tuple[bytes, bytes]:
    """
    Encrypt data with a password. Returns (salt, ciphertext).
    Salt must be stored for decryption.
    """
    salt = os.urandom(SALT_SIZE)
    key = derive_key(password, salt)
    f = Fernet(key)
    ciphertext = f.encrypt(data)
    return salt, ciphertext


def decrypt_with_password(salt: bytes, ciphertext: bytes, password: str) -> bytes:
    """
    Decrypt data with a password and salt. Raises InvalidToken if password is wrong.
    """
    key = derive_key(password, salt)
    f = Fernet(key)
    return f.decrypt(ciphertext) 