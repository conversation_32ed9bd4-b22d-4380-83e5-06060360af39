openapi: 3.0.3
info:
  title: Cloud OTP API
  version: "1.0.0"
  description: |-
    Secure, multi-tenant, cloud-native backend for a One-Time Password (OTP) management platform.
    This API is designed for both individual and enterprise use, with a strong emphasis on security, collaboration, and automation.
    The platform is built on a zero-knowledge encryption model, ensuring that user vault data (OTP secrets, notes) is end-to-end encrypted. The server stores an encrypted Data Encryption Key (DEK) that can only be decrypted with the user's secondary password, which is never sent to the server.
  contact:
    name: Cloud OTP Support
    email: <EMAIL>
servers:
  - url: /api
    description: Cloud OTP API Server
tags:
  - name: Authentication
    description: User registration, login, and OAuth 2.0 social login management.
  - name: Security
    description: End-to-end encryption, secondary password management, and account lockout mechanisms.
  - name: OTP Management
    description: Core CRUD operations for creating, reading, updating, and deleting OTP entries.
  - name: Folder Management
    description: Endpoints for organizing OTPs into a hierarchical folder structure.
  - name: Sharing
    description: Endpoints for sharing individual OTPs and entire folders with other users or teams.
  - name: Team Management
    description: Operations for creating teams, managing members, roles, invitations, and viewing team activity.
  - name: API Keys
    description: Management of API keys for programmatic access and automation.
  - name: Rate Limiting
    description: Endpoints for checking API usage status and managing limits.
  - name: Webhooks
    description: Configuration and management of webhooks for real-time event notifications.
  - name: SSO/SAML
    description: Enterprise Single Sign-On (SSO) integration using SAML 2.0.
  - name: Campaigns & Marketing
    description: Management of marketing/sales campaigns, coupons, and invitations.
  - name: Payments
    description: Endpoints for processing payment provider webhooks (e.g., Paddle).
  - name: Support
    description: Endpoints for managing user support tickets.
  - name: Compliance
    description: Endpoints for GDPR/CCPA data export and account deletion requests.
  - name: Admin
    description: General administrative functions for system-wide management.
  - name: Developer Admin
    description: Administrative functions for managing developer accounts and resource allocation.
  - name: Analytics
    description: Endpoints for retrieving aggregated analytics and system health reports.
  - name: Notifications
    description: Endpoints for managing in-app notifications for administrators.
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: A JSON Web Token obtained from the `/auth/login` endpoint.
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: An API key generated from the `/keys` endpoint for programmatic access.
  schemas:
    Error:
      type: object
      properties:
        msg:
          type: string
          description: A human-readable error message.
        error:
          type: string
          description: A short string identifying the error type (e.g., 'Not Found').
        details:
          type: object
          additionalProperties: true
          description: Additional details about the error, often present for validation failures.
    PaginatedResponse:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items matching the query.
        page:
          type: integer
          description: The current page number.
        per_page:
          type: integer
          description: The number of items per page.
        pages:
          type: integer
          description: The total number of pages.
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        is_active:
          type: boolean
        credits:
          type: integer
        api_quota_per_month:
          type: integer
          nullable: true
          description: The maximum number of API requests the user can make per month.
        api_rate_limit_per_second:
          type: integer
          nullable: true
          description: The maximum number of API requests the user can make per second.
        storage_limit:
          type: integer
          nullable: true
          description: The maximum number of storable items (OTPs, notes, etc.).
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        referred_by_id:
          type: integer
          nullable: true
        campaign_id:
          type: integer
          nullable: true
    Role:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        description:
          type: string
          nullable: true
    Team:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        owner:
          $ref: '#/components/schemas/User'
          readOnly: true
        members:
          type: array
          items:
            $ref: '#/components/schemas/TeamMember'
          readOnly: true
        credits:
          type: integer
        api_quota_per_month:
          type: integer
          nullable: true
        api_rate_limit_per_second:
          type: integer
          nullable: true
        storage_limit:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    TeamMember:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          $ref: '#/components/schemas/User'
          readOnly: true
        role:
          type: string
          enum: [admin, member, read-only]
          description: The role of the member within the team.
    OTP:
      type: object
      required:
        - name
        - secret_encrypted
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        secret_encrypted:
          type: string
          description: The OTP secret, encrypted client-side with the user's Data Encryption Key (DEK). The server never sees the plaintext secret.
        algorithm:
          type: string
          default: SHA1
        digits:
          type: integer
          default: 6
        period:
          type: integer
          default: 30
        counter:
          type: integer
          nullable: true
          description: Required for HOTP (counter-based) tokens.
        metadata:
          type: object
          additionalProperties: true
        folder_id:
          type: integer
          nullable: true
        owner_id:
          type: integer
          nullable: true
          readOnly: true
        team_id:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Folder:
      type: object
      required:
        - name
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        description:
          type: string
          nullable: true
        owner_id:
          type: integer
          nullable: true
          readOnly: true
        team_id:
          type: integer
          nullable: true
        parent_id:
          type: integer
          nullable: true
          description: The ID of the parent folder, for creating nested structures.
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Note:
      type: object
      required:
        - otp_id
        - content_encrypted
      properties:
        id:
          type: integer
          readOnly: true
        otp_id:
          type: integer
        author_id:
          type: integer
          readOnly: true
        title:
          type: string
        content_encrypted:
          type: string
          description: The note content, encrypted client-side with the user's Data Encryption Key (DEK).
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    APIKey:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        prefix:
          type: string
          readOnly: true
          description: A unique prefix to identify the key. The full key is only shown on creation.
        expires_at:
          type: string
          format: date-time
          nullable: true
        last_used_at:
          type: string
          format: date-time
          nullable: true
          readOnly: true
        is_active:
          type: boolean
        user_id:
          type: integer
          nullable: true
          readOnly: true
        team_id:
          type: integer
          nullable: true
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    APIKeyCreated:
      allOf:
        - $ref: '#/components/schemas/APIKey'
        - type: object
          properties:
            key:
              type: string
              description: The full, unhashed API key. This is only returned on creation and must be stored securely by the client.
    OTPShare:
      type: object
      required:
        - otp_id
      properties:
        id:
          type: integer
          readOnly: true
        otp_id:
          type: integer
        shared_by_user_id:
          type: integer
          readOnly: true
        shared_with_user_id:
          type: integer
          nullable: true
        shared_with_team_id:
          type: integer
          nullable: true
        share_type:
          type: string
          enum: [user, team, public]
        permission:
          type: string
          enum: [read, write, admin]
        expires_at:
          type: string
          format: date-time
          nullable: true
        access_limit:
          type: integer
          nullable: true
        access_count:
          type: integer
          readOnly: true
        is_active:
          type: boolean
        share_token:
          type: string
          nullable: true
          readOnly: true
          description: A unique token for accessing public shares.
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    FolderShare:
      type: object
      required:
        - folder_id
      properties:
        id:
          type: integer
          readOnly: true
        folder_id:
          type: integer
        shared_by_user_id:
          type: integer
          readOnly: true
        shared_with_user_id:
          type: integer
          nullable: true
        shared_with_team_id:
          type: integer
          nullable: true
        share_type:
          type: string
          enum: [user, team]
        permission:
          type: string
          enum: [read, write, admin]
        expires_at:
          type: string
          format: date-time
          nullable: true
        access_limit:
          type: integer
          nullable: true
        access_count:
          type: integer
          readOnly: true
        is_active:
          type: boolean
        share_token:
          type: string
          nullable: true
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    SecondaryPasswordAttempt:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        ip_address:
          type: string
        success:
          type: boolean
        user_agent:
          type: string
          nullable: true
    AuditLog:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user_id:
          type: integer
          nullable: true
        team_id:
          type: integer
          nullable: true
        action:
          type: string
        details:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
          readOnly: true
    SystemSetting:
      type: object
      properties:
        key:
          type: string
        value:
          type: object
          additionalProperties: true
          description: The value of the setting, which can be any JSON type.
    Coupon:
      type: object
      required:
        - code
        - discount_type
        - value
      properties:
        id:
          type: integer
          readOnly: true
        code:
          type: string
        discount_type:
          type: string
          enum: [percentage, fixed_amount, credits]
        value:
          type: integer
          description: The value of the discount (in cents for fixed_amount, basis points for percentage, or number of credits).
        expires_at:
          type: string
          format: date-time
          nullable: true
        max_uses:
          type: integer
          nullable: true
        use_count:
          type: integer
          readOnly: true
        is_active:
          type: boolean
        campaign_id:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Invitation:
      type: object
      required:
        - code
        - created_by_id
      properties:
        id:
          type: integer
          readOnly: true
        code:
          type: string
        created_by_id:
          type: integer
        team_id:
          type: integer
          nullable: true
        email:
          type: string
          format: email
          nullable: true
        token:
          type: string
          nullable: true
          description: A secure, single-use token for accepting team invitations.
        status:
          type: string
          enum: [pending, accepted, revoked, expired]
        expires_at:
          type: string
          format: date-time
          nullable: true
        is_active:
          type: boolean
        campaign_id:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Ticket:
      type: object
      required:
        - subject
      properties:
        id:
          type: integer
          readOnly: true
        subject:
          type: string
        status:
          type: string
          enum: [open, in_progress, closed]
        user:
          $ref: '#/components/schemas/User'
          readOnly: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/TicketMessage'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    TicketMessage:
      type: object
      required:
        - message
      properties:
        id:
          type: integer
          readOnly: true
        user:
          $ref: '#/components/schemas/User'
          readOnly: true
        message:
          type: string
        created_at:
          type: string
          format: date-time
          readOnly: true
    WebhookConfig:
      type: object
      required:
        - target_url
      properties:
        id:
          type: integer
          readOnly: true
        user_id:
          type: integer
          nullable: true
          readOnly: true
        team_id:
          type: integer
          nullable: true
          writeOnly: true
        target_url:
          type: string
          format: uri
        secret:
          type: string
          nullable: true
          writeOnly: true
          description: A secret string used to sign webhook payloads. It is write-only and not returned in responses.
        is_active:
          type: boolean
          default: true
        subscribed_events:
          type: string
          default: '*'
          description: A comma-separated list of event types to subscribe to (e.g., 'otp.shared,user.registered'). Use '*' for all events.
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    WebhookEvent:
      type: object
      properties:
        id:
          type: integer
        event_type:
          type: string
        payload:
          type: object
          additionalProperties: true
        target_url:
          type: string
          format: uri
        status:
          type: string
          enum: [pending, sent, failed]
        attempts:
          type: integer
        last_attempt_at:
          type: string
          format: date-time
          nullable: true
        response_status:
          type: integer
          nullable: true
        response_body:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    SAMLIdentityProvider:
      type: object
      required:
        - team_id
        - idp_name
        - entity_id
        - sso_url
        - x509_cert
      properties:
        id:
          type: integer
          readOnly: true
        team_id:
          type: integer
        idp_name:
          type: string
          description: A human-readable name for the Identity Provider.
        entity_id:
          type: string
          description: The Entity ID of the SAML Identity Provider.
        sso_url:
          type: string
          format: uri
          description: The Single Sign-On URL (HTTP-Redirect binding) of the IdP.
        x509_cert:
          type: string
          writeOnly: true
          description: The IdP's X.509 signing certificate (PEM format, without headers/footers).
        slo_url:
          type: string
          format: uri
          nullable: true
          description: The Single Logout URL (HTTP-Redirect binding) of the IdP.
        attribute_mapping:
          type: object
          additionalProperties:
            type: string
            description: A JSON object to map SAML attributes to user profile fields (e.g., `{"email": "EmailAddress", "first_name": "FirstName"}`).
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Campaign:
      type: object
      required:
        - name
        - type
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        type:
          type: string
          description: The type of campaign (e.g., 'marketing', 'sales').
        description:
          type: string
          nullable: true
        start_date:
          type: string
          format: date-time
          nullable: true
        end_date:
          type: string
          format: date-time
          nullable: true
        is_active:
          type: boolean
        metadata:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Referral:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        referred_user_id:
          type: integer
        referrer_user_id:
          type: integer
          nullable: true
        campaign_id:
          type: integer
          nullable: true
        invitation_id:
          type: integer
          nullable: true
        coupon_id:
          type: integer
          nullable: true
        method:
          type: string
          nullable: true
          description: The method used for the referral (e.g., 'invitation', 'coupon').
        metadata:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    Notification:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user_id:
          type: integer
          nullable: true
        team_id:
          type: integer
          nullable: true
        type:
          type: string
          description: The type of notification (e.g., 'system_alert', 'credit_low').
        severity:
          type: string
          enum: [info, warning, critical]
        message:
          type: string
        metadata:
          type: object
          additionalProperties: true
        is_read:
          type: boolean
        is_dismissed:
          type: boolean
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    TeamResourceAllocation:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team_id:
          type: integer
        resource_type:
          type: string
          description: The type of resource being allocated (e.g., 'users', 'api_keys').
        limit:
          type: integer
          description: The allocated limit for the resource.
        used:
          type: integer
          description: The current usage of the resource.
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
paths:
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: |-
        Creates a new user account. If `secondary_password` and `encrypted_dek` are provided, zero-knowledge encryption is enabled for the user's vault from the start.
        Also handles user acquisition tracking via `invitation_code`, `coupon_code`, and `referrer_id`. A `Referral` record is created to attribute the signup.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                secondary_password:
                  type: string
                  minLength: 16
                  description: The user's vault password. It is never sent to the server, only used client-side to encrypt the DEK.
                encrypted_dek:
                  type: string
                  description: The user's Data Encryption Key, encrypted with the `secondary_password`.
                invitation_code:
                  type: string
                  description: An invitation code used for signup.
                coupon_code:
                  type: string
                  description: A coupon code used for signup.
                referrer_id:
                  type: integer
                  description: The ID of the user who referred this new user.
      responses:
        '201':
          description: User created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Email and password are required.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: A user with the provided email already exists.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /auth/login:
    post:
      tags:
        - Authentication
      summary: Authenticate with email and password
      description: Authenticates a user with their email and primary password and returns a JWT access token.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        '200':
          description: Authentication successful.
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    description: The JWT access token for authenticating subsequent requests.
        '401':
          description: Invalid credentials.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /auth/login/{provider}:
    get:
      tags:
        - Authentication
      summary: Initiate OAuth 2.0 social login
      description: Redirects the user to the specified OAuth provider to begin the authentication flow.
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [google, github, microsoft, linkedin, facebook, oidc]
      responses:
        '302':
          description: Redirect to the OAuth provider's authorization URL.
  /auth/authorize/{provider}:
    get:
      tags:
        - Authentication
      summary: OAuth 2.0 callback endpoint
      description: The callback URL that the OAuth provider redirects to after user authorization. The server exchanges the authorization code for an access token, fetches user info, provisions a new user if necessary, and returns a JWT. This endpoint is intended for browser redirection and not direct API calls.
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [google, github, microsoft, linkedin, facebook, oidc]
      responses:
        '200':
          description: OAuth flow successful. Returns a JWT and user info.
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: OAuth authorization failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /auth/providers:
    get:
      tags:
        - Authentication
      summary: List available OAuth providers
      description: Returns a list of OAuth 2.0 providers that are configured and enabled on the server.
      responses:
        '200':
          description: A list of available provider names.
          content:
            application/json:
              schema:
                type: object
                properties:
                  providers:
                    type: array
                    items:
                      type: string
                    example: [google, github]
  /auth/me:
    get:
      tags:
        - Authentication
      summary: Get current user information
      description: Retrieves the profile information for the currently authenticated user.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Current user's profile data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Authentication required.
  /auth/me/export-vault:
    post:
      tags:
        - Security
      summary: Export user's encrypted vault
      description: |-
        Exports all of the user's personal OTPs, folders, and notes into a single JSON object, which is then encrypted with the provided secondary password.
        This allows for secure, portable backups of the user's vault. The output is a base64-encoded salt and ciphertext.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - secondary_password
              properties:
                secondary_password:
                  type: string
                  description: The user's secondary (vault) password, used to encrypt the exported data.
      responses:
        '200':
          description: Encrypted vault data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  salt:
                    type: string
                    description: Base64-encoded salt used for encryption.
                  encrypted_vault:
                    type: string
                    description: Base64-encoded ciphertext of the user's vault.
        '400':
          description: Secondary password is required.
  /auth/me/import-vault:
    post:
      tags:
        - Security
      summary: Import and restore an encrypted vault
      description: |-
        Imports an encrypted vault from a previous export. This is a destructive operation that **replaces all existing OTPs, folders, and notes** for the user with the content from the imported file.
        The user must provide their secondary password to decrypt the vault data.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - secondary_password
                - salt
                - encrypted_vault
              properties:
                secondary_password:
                  type: string
                  description: The user's secondary (vault) password to decrypt the data.
                salt:
                  type: string
                  description: Base64-encoded salt from the export.
                encrypted_vault:
                  type: string
                  description: Base64-encoded ciphertext from the export.
      responses:
        '200':
          description: Vault imported successfully.
        '400':
          description: Required fields are missing, or decryption/parsing failed.
  /users/me/export-data:
    get:
      tags:
        - Compliance
      summary: Export all personal data (GDPR/CCPA)
      description: |-
        Allows a user to download all of their personal data in a machine-readable JSON format for compliance with data portability regulations like GDPR and CCPA.
        The export includes user profile, OTPs, folders, notes, API keys, audit logs, team memberships, support tickets, payments, shares, and webhook configurations.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A JSON object containing all of the user's data.
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Authentication required.
  /users/me/request-account-deletion:
    post:
      tags:
        - Compliance
      summary: Request permanent account deletion (GDPR/CCPA)
      description: |-
        Initiates an immediate and irreversible deletion of the user's account and all associated data, in compliance with the "right to be forgotten".
        **This action cannot be undone.** All data, including the user record itself, is permanently removed from the database.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Confirmation that the account and all data have been deleted.
        '401':
          description: Authentication required.
  /secondary-password/set:
    post:
      tags:
        - Security
      summary: Set or update secondary password
      description: |-
        Sets or updates the user's secondary (vault) password and the associated encrypted Data Encryption Key (DEK).
        If a secondary password already exists, the `current_secondary_password` must be provided for verification.
        This is the core of the zero-knowledge model.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - secondary_password
                - encrypted_dek
              properties:
                secondary_password:
                  type: string
                  minLength: 16
                  description: The new secondary password.
                encrypted_dek:
                  type: string
                  description: The user's Data Encryption Key, newly encrypted with the `secondary_password`.
                current_secondary_password:
                  type: string
                  description: The user's existing secondary password, required only when updating.
      responses:
        '200':
          description: Secondary password updated successfully.
        '400':
          description: Invalid input, such as a password that is too short.
        '401':
          description: The provided `current_secondary_password` was incorrect.
  /secondary-password/verify:
    post:
      tags:
        - Security
      summary: Verify secondary password and get DEK
      description: |-
        Verifies the user's secondary password. On success, it returns the `encrypted_dek`, which the client can then decrypt to access the vault.
        This endpoint is subject to a strict lockout policy: after 5 failed attempts within 24 hours, the account is locked with an exponentially increasing duration.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - secondary_password
              properties:
                secondary_password:
                  type: string
      responses:
        '200':
          description: Password verified successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  msg:
                    type: string
                  encrypted_dek:
                    type: string
        '401':
          description: Invalid secondary password. The response may include the number of attempts remaining.
        '404':
          description: No secondary password has been set for this user.
        '423':
          description: Account is locked due to too many failed attempts. The response includes the lockout end time.
  /secondary-password/status:
    get:
      tags:
        - Security
      summary: Get secondary password status
      description: Checks the status of the user's secondary password setup, including whether it's configured and if the account is currently locked out.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Status of the secondary password.
          content:
            application/json:
              schema:
                type: object
                properties:
                  has_secondary_password:
                    type: boolean
                  has_encrypted_dek:
                    type: boolean
                  is_locked_out:
                    type: boolean
                  lockout_message:
                    type: string
                    nullable: true
                  recent_failed_attempts:
                    type: integer
                  setup_complete:
                    type: boolean
  /secondary-password/attempts:
    get:
      tags:
        - Security
      summary: Get recent password attempts
      description: Retrieves a list of the last 50 secondary password verification attempts for the current user, including timestamp, IP address, and success status.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of recent attempts.
          content:
            application/json:
              schema:
                type: object
                properties:
                  attempts:
                    type: array
                    items:
                      $ref: '#/components/schemas/SecondaryPasswordAttempt'
  /otp/:
    get:
      tags:
        - OTP Management
      summary: List accessible OTPs
      description: |-
        Retrieves a list of all OTP entries accessible to the authenticated user. This includes:
        1. OTPs owned directly by the user.
        2. OTPs belonging to teams the user is a member of.
        3. OTPs explicitly shared with the user.
        4. OTPs located in folders (and sub-folders) that have been shared with the user or their teams.
        The endpoint can be filtered by `folder_id` or `team_id`.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: query
          schema:
            type: integer
          description: Filter OTPs by a specific folder ID.
        - name: team_id
          in: query
          schema:
            type: integer
          description: Filter OTPs by a specific team ID.
      responses:
        '200':
          description: A list of OTP objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OTP'
        '401':
          description: Authentication required.
    post:
      tags:
        - OTP Management
      summary: Create a new OTP entry
      description: Creates a new OTP entry, owned by the user or a team they have write access to. The `secret_encrypted` field must contain the OTP secret already encrypted on the client side.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OTP'
      responses:
        '201':
          description: OTP created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OTP'
        '400':
          description: Name and encrypted secret are required.
        '403':
          description: Insufficient permissions for the specified team.
  /otp/{otp_id}:
    get:
      tags:
        - OTP Management
      summary: Get a specific OTP entry
      description: Retrieves the details of a single OTP entry, provided the user has read access.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OTP details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OTP'
        '403':
          description: Access denied.
        '404':
          description: OTP not found.
    put:
      tags:
        - OTP Management
      summary: Update an OTP entry
      description: Updates the details of an OTP entry, provided the user has write access.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OTP'
      responses:
        '200':
          description: OTP updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OTP'
        '403':
          description: Access denied.
        '404':
          description: OTP not found.
    delete:
      tags:
        - OTP Management
      summary: Delete an OTP entry
      description: Permanently deletes an OTP entry, provided the user has delete/admin access.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: OTP deleted successfully.
        '403':
          description: Access denied.
        '404':
          description: OTP not found.
  /otp/folders:
    get:
      tags:
        - Folder Management
      summary: List accessible folders
      description: Retrieves a list of all folders accessible to the user, including their own and those from teams they are a member of. Can be filtered by `team_id` or `parent_id` (for top-level folders).
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: team_id
          in: query
          schema:
            type: integer
        - name: parent_id
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: A list of folder objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Folder'
    post:
      tags:
        - Folder Management
      summary: Create a new folder
      description: Creates a new folder, owned by the user or a team they have write access to.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Folder'
      responses:
        '201':
          description: Folder created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folder'
        '400':
          description: Folder name is required.
        '403':
          description: Insufficient permissions for the specified team.
  /otp/folders/{folder_id}:
    get:
      tags:
        - Folder Management
      summary: Get a specific folder
      description: Retrieves details for a single folder, provided the user has access.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Folder details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folder'
        '403':
          description: Access denied.
        '404':
          description: Folder not found.
    put:
      tags:
        - Folder Management
      summary: Update a folder
      description: Updates a folder's details (name, description, parent), provided the user has write access.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Folder'
      responses:
        '200':
          description: Folder updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folder'
        '403':
          description: Access denied.
        '404':
          description: Folder not found.
    delete:
      tags:
        - Folder Management
      summary: Delete a folder
      description: Permanently deletes a folder. Only the owner or a team admin can perform this action.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Folder deleted successfully.
        '403':
          description: Access denied.
        '404':
          description: Folder not found.
  /otp/{otp_id}/notes:
    get:
      tags:
        - OTP Management
      summary: List notes for an OTP
      description: Retrieves all notes associated with a specific OTP entry.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: A list of note objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Note'
        '403':
          description: Access denied.
        '404':
          description: OTP not found.
    post:
      tags:
        - OTP Management
      summary: Add a note to an OTP
      description: Adds a new, encrypted note to an OTP entry. The user must have write access to the OTP.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Note'
      responses:
        '201':
          description: Note created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Note'
        '400':
          description: Encrypted content is required.
        '403':
          description: Access denied.
  /otp/notes/{note_id}:
    put:
      tags:
        - OTP Management
      summary: Update a note
      description: Updates the content or title of a note. Only the original author of the note can update it.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: note_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Note'
      responses:
        '200':
          description: Note updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Note'
        '403':
          description: Access denied.
        '404':
          description: Note not found.
    delete:
      tags:
        - OTP Management
      summary: Delete a note
      description: Permanently deletes a note. Only the original author of the note can delete it.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: note_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Note deleted successfully.
        '403':
          description: Access denied.
        '404':
          description: Note not found.
  /otp/{otp_id}/share:
    post:
      tags:
        - Sharing
      summary: Share an OTP
      description: Shares an OTP with another user or creates a public share link. Requires write access to the OTP.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
              properties:
                type:
                  type: string
                  enum: [user, public]
                  description: The type of share to create.
                email:
                  type: string
                  format: email
                  description: Required if `type` is 'user'.
                permission:
                  type: string
                  enum: [read, write, admin]
                  default: read
                expires_at:
                  type: string
                  format: date-time
                  nullable: true
                password_hash:
                  type: string
                  description: Optional password for public shares.
                access_limit:
                  type: integer
                  description: Optional access limit for public shares.
      responses:
        '201':
          description: OTP shared successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OTPShare'
        '400':
          description: Invalid share type.
        '403':
          description: Access denied.
        '404':
          description: User or OTP not found.
        '409':
          description: Already shared with this user.
  /otp/{otp_id}/shares:
    get:
      tags:
        - Sharing
      summary: List shares for an OTP
      description: Retrieves all active shares for a specific OTP. Requires read access to the OTP.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: otp_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: A list of OTPShare objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OTPShare'
        '403':
          description: Access denied.
        '404':
          description: OTP not found.
  /otp/shares/{share_id}:
    delete:
      tags:
        - Sharing
      summary: Revoke an OTP share
      description: Deactivates an OTP share, revoking access. Can be done by the user who created the share or an owner/admin of the OTP.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: share_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Share revoked successfully.
        '403':
          description: Access denied.
        '404':
          description: Share not found.
  /otp/folders/{folder_id}/share:
    post:
      tags:
        - Sharing
      summary: Share a folder
      description: Shares a folder and all its contents (including sub-folders) with another user or an entire team. Requires admin permission on the folder.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
              properties:
                type:
                  type: string
                  enum: [user, team]
                email:
                  type: string
                  format: email
                  description: Required if `type` is 'user'.
                team_id:
                  type: integer
                  description: Required if `type` is 'team'.
                permission:
                  type: string
                  enum: [read, write, admin]
                  default: read
                expires_at:
                  type: string
                  format: date-time
                  nullable: true
      responses:
        '201':
          description: Folder shared successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FolderShare'
        '400':
          description: Invalid input.
        '403':
          description: Only folder owners or team admins can share.
        '404':
          description: User, team, or folder not found.
        '409':
          description: Folder already shared with this user/team.
  /otp/folders/{folder_id}/shares:
    get:
      tags:
        - Sharing
      summary: List shares for a folder
      description: Retrieves all active shares for a specific folder. Requires read access to the folder.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: folder_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: A list of FolderShare objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FolderShare'
        '403':
          description: Access denied.
        '404':
          description: Folder not found.
  /otp/shares/folder/{share_id}:
    delete:
      tags:
        - Sharing
      summary: Revoke a folder share
      description: Deactivates a folder share, revoking access. Can be done by the user who created the share or an owner/admin of the folder.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: share_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Share revoked successfully.
        '403':
          description: Access denied.
        '404':
          description: Share not found.
  /teams/:
    post:
      tags:
        - Team Management
      summary: Create a new team
      description: Creates a new team with the authenticated user as the owner.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
      responses:
        '201':
          description: Team created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
  /teams/{team_id}:
    get:
      tags:
        - Team Management
      summary: Get team details
      description: Retrieves details for a specific team.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Team details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
        '404':
          description: Team not found.
  /teams/{team_id}/members:
    get:
      tags:
        - Team Management
      summary: List team members
      description: Retrieves a paginated list of all members in a team. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: A paginated list of team members.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/TeamMember'
        '403':
          description: Only team admins/owners can view members.
        '404':
          description: Team not found.
    post:
      tags:
        - Team Management
      summary: Add a member to a team
      description: Directly adds an existing user to a team. Only the team owner can perform this action. For a more standard workflow, use the invitation endpoint.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                role:
                  type: string
                  enum: [admin, member, read-only]
                  default: member
      responses:
        '201':
          description: Member added successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamMember'
        '403':
          description: Only team owners can add members.
        '404':
          description: Team or user not found.
  /teams/{team_id}/members/{member_id}:
    delete:
      tags:
        - Team Management
      summary: Remove a member from a team
      description: Removes a user from a team. Only the team owner can perform this action.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
        - name: member_id
          in: path
          required: true
          schema:
            type: integer
            description: The ID of the `TeamMember` record, not the user ID.
      responses:
        '204':
          description: Member removed successfully.
        '403':
          description: Only team owners can remove members.
        '404':
          description: Team or member not found.
  /teams/{team_id}/members/{user_id}:
    patch:
      tags:
        - Team Management
      summary: Change a member's role
      description: Updates the role of a team member. Cannot be used to change the team owner's role. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
            description: The ID of the user whose role is being changed.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role
              properties:
                role:
                  type: string
                  enum: [admin, member, read-only]
      responses:
        '200':
          description: Role updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamMember'
        '400':
          description: Invalid role or cannot change owner's role.
        '403':
          description: Only team admins/owners can change roles.
        '404':
          description: Team or member not found.
  /teams/{team_id}/invite:
    post:
      tags:
        - Team Management
      summary: Invite a user to a team via email
      description: Creates a pending invitation and sends an email with a secure, expiring link to the specified address. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '201':
          description: Invitation created and email queued for sending.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Invitation'
        '400':
          description: Email is required.
        '403':
          description: Only team admins/owners can invite members.
        '409':
          description: An invitation has already been sent to this email.
  /teams/invitations/accept:
    post:
      tags:
        - Team Management
      summary: Accept a team invitation
      description: Allows a user to accept a team invitation using the token from the invitation email. The user must already have an account with the invited email address.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
              properties:
                token:
                  type: string
      responses:
        '200':
          description: Invitation accepted, user is now a member of the team.
        '400':
          description: Token is required.
        '404':
          description: Invalid or expired invitation token, or no user found with the invited email.
  /teams/invitations/{invitation_id}:
    delete:
      tags:
        - Team Management
      summary: Revoke a team invitation
      description: Revokes a pending invitation, making the link invalid. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: invitation_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Invitation revoked successfully.
        '400':
          description: Only pending invitations can be revoked.
        '403':
          description: Only team admins/owners can revoke invitations.
        '404':
          description: Invitation not found.
  /teams/{team_id}/activity:
    get:
      tags:
        - Team Management
      summary: List team activity logs
      description: Retrieves a paginated list of audit logs for a specific team. Team admins/owners can view all logs, while members can only view their own actions. Logs can be filtered by action, user, and date range.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
        - name: action
          in: query
          schema:
            type: string
        - name: user_id
          in: query
          schema:
            type: integer
        - name: start_date
          in: query
          schema:
            type: string
            format: date-time
        - name: end_date
          in: query
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: A paginated list of audit logs.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/AuditLog'
        '403':
          description: Not authorized to view logs for this team.
        '404':
          description: Team not found.
  /teams/{team_id}/resources:
    get:
      tags:
        - Team Management
      summary: Get team resource allocations
      description: Retrieves a paginated list of resource allocations (e.g., max users, API keys) for a team. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: A paginated list of resource allocations.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/TeamResourceAllocation'
        '403':
          description: Only team admins/owners can view resource allocations.
        '404':
          description: Team not found.
    patch:
      tags:
        - Team Management
      summary: Update team resource allocations
      description: Updates or creates resource allocation limits for a team. Requires team admin/owner permissions.
      security:
        - bearerAuth: []
      parameters:
        - name: team_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                resources:
                  type: array
                  items:
                    type: object
                    required:
                      - resource_type
                      - limit
                    properties:
                      resource_type:
                        type: string
                      limit:
                        type: integer
      responses:
        '200':
          description: Resource allocations updated successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  updated:
                    type: array
                    items:
                      $ref: '#/components/schemas/TeamResourceAllocation'
        '403':
          description: Only team admins/owners can update resource allocations.
        '404':
          description: Team not found.
  /keys/:
    get:
      tags:
        - API Keys
      summary: List active API keys
      description: Retrieves a list of all active API keys for the authenticated user. The full key is not returned, only its metadata.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of API key metadata.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIKey'
    post:
      tags:
        - API Keys
      summary: Create a new API key
      description: Creates a new API key for the authenticated user. The full key is returned **only once** upon creation and must be stored securely by the client.
      security:
        - bearerAuth: []
      responses:
        '201':
          description: API key created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIKeyCreated'
  /keys/{key_id}:
    delete:
      tags:
        - API Keys
      summary: Revoke an API key
      description: Deactivates an API key, preventing it from being used for future requests. This is a non-destructive action (the key record is kept but marked as inactive).
      security:
        - bearerAuth: []
      parameters:
        - name: key_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: API key revoked successfully.
        '404':
          description: API key not found or does not belong to the user.
  /rate-limiting/status:
    get:
      tags:
        - Rate Limiting
      summary: Get current API usage status
      description: Retrieves the current API rate limit and monthly quota status for the authenticated user, including usage, remaining requests, and reset time.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Current rate limit and quota status.
          content:
            application/json:
              schema:
                type: object
                properties:
                  rate_limit:
                    type: object
                    properties:
                      requests_per_second:
                        type: integer
                      current_usage:
                        type: integer
                  quota:
                    type: object
                    properties:
                      monthly_limit:
                        type: integer
                      used:
                        type: integer
                      remaining:
                        type: integer
                      percentage_used:
                        type: number
                        format: float
                      resets_at:
                        type: string
                        format: date-time
  /rate-limiting/usage-history:
    get:
      tags:
        - Rate Limiting
      summary: Get API usage history
      description: Retrieves the user's API request history, including daily request counts for the last 30 days and a breakdown of requests by endpoint for the current month.
      security:
        - bearerAuth: []
      parameters:
        - name: days
          in: query
          schema:
            type: integer
            default: 30
            maximum: 90
      responses:
        '200':
          description: API usage history.
          content:
            application/json:
              schema:
                type: object
                properties:
                  period_days:
                    type: integer
                  daily_usage:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          format: date
                        requests:
                          type: integer
                  endpoint_usage:
                    type: array
                    items:
                      type: object
                      properties:
                        endpoint:
                          type: string
                        requests:
                          type: integer
  /webhooks/config:
    get:
      tags:
        - Webhooks
      summary: List webhook configurations
      description: Retrieves a list of all webhook configurations accessible to the user, including their personal configs and those for teams where they are an admin or owner.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of webhook configurations.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WebhookConfig'
    post:
      tags:
        - Webhooks
      summary: Create a webhook configuration
      description: Creates a new webhook endpoint for the authenticated user or for a team they administer.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookConfig'
      responses:
        '201':
          description: Webhook configuration created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookConfig'
        '403':
          description: Insufficient permissions for the specified team.
  /webhooks/config/{config_id}:
    get:
      tags:
        - Webhooks
      summary: Get a specific webhook configuration
      description: Retrieves the details of a single webhook configuration, provided the user has access.
      security:
        - bearerAuth: []
      parameters:
        - name: config_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Webhook configuration details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookConfig'
        '403':
          description: Access denied.
        '404':
          description: Configuration not found.
    put:
      tags:
        - Webhooks
      summary: Update a webhook configuration
      description: Updates an existing webhook configuration.
      security:
        - bearerAuth: []
      parameters:
        - name: config_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookConfig'
      responses:
        '200':
          description: Configuration updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookConfig'
        '403':
          description: Access denied.
        '404':
          description: Configuration not found.
    delete:
      tags:
        - Webhooks
      summary: Delete a webhook configuration
      description: Deletes a webhook configuration.
      security:
        - bearerAuth: []
      parameters:
        - name: config_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Configuration deleted successfully.
        '403':
          description: Access denied.
        '404':
          description: Configuration not found.
  /sso/saml/metadata:
    get:
      tags:
        - SSO/SAML
      summary: Get SAML Service Provider metadata
      description: |-
        Generates and returns the SAML 2.0 Service Provider (SP) metadata as an XML file.
        This metadata is required by the Identity Provider (IdP) to configure the SSO connection.
        A `team_id` is required to generate the correct ACS URL and signing certificates for that team's configuration.
      parameters:
        - name: team_id
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: SAML SP metadata.
          content:
            application/xml:
              schema:
                type: string
                format: xml
        '400':
          description: team_id is required.
        '404':
          description: No active SAML configuration found for the specified team.
  /sso/saml/login:
    post:
      tags:
        - SSO/SAML
      summary: Initiate SAML SSO login
      description: |-
        Starts the SP-initiated SAML login flow. The server generates a SAML AuthnRequest and redirects the user to the Identity Provider's SSO URL.
        This endpoint is typically called by a frontend "Login with SSO" button.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - team_id
              properties:
                team_id:
                  type: integer
                relay_state:
                  type: string
                  nullable: true
                  description: An optional state parameter to be passed through the login flow, often used for post-login redirects.
      responses:
        '302':
          description: Redirect to the IdP's SSO URL. The `Location` header contains the redirect target.
        '400':
          description: team_id is required.
        '404':
          description: No active SAML configuration found for the specified team.
        '500':
          description: Failed to prepare the SAML AuthnRequest.
  /sso/saml/acs:
    post:
      tags:
        - SSO/SAML
      summary: SAML Assertion Consumer Service (ACS)
      description: |-
        The callback endpoint where the Identity Provider sends the SAML assertion after successful authentication.
        The server validates the assertion, finds or provisions a user based on the attributes, and issues a JWT.
        This endpoint consumes `application/x-www-form-urlencoded` data as per the SAML standard.
      parameters:
        - name: team_id
          in: query
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - SAMLResponse
              properties:
                SAMLResponse:
                  type: string
                  description: The Base64-encoded SAML assertion from the IdP.
                RelayState:
                  type: string
                  nullable: true
                  description: The relay state passed from the initial login request.
      responses:
        '200':
          description: Authentication successful. Returns a JWT.
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
                  relay_state:
                    type: string
        '400':
          description: Invalid or missing SAMLResponse.
        '401':
          description: SAML authentication failed (e.g., invalid signature, expired assertion).
        '404':
          description: No active SAML configuration found for the specified team.
  /sso/saml/slo:
    post:
      tags:
        - SSO/SAML
      summary: Initiate SAML Single Logout (SLO)
      description: Starts the SP-initiated SAML Single Logout flow. The server generates a SAML LogoutRequest and redirects the user to the Identity Provider's SLO URL.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - team_id
              properties:
                team_id:
                  type: integer
      responses:
        '302':
          description: Redirect to the IdP's SLO URL. The `Location` header contains the redirect target.
        '404':
          description: No SLO URL configured for the specified team.
  /coupons/validate/{code}:
    post:
      tags:
        - Campaigns & Marketing
      summary: Validate a coupon code
      description: Checks if a coupon code is valid, active, not expired, and has not reached its usage limit. Returns the coupon's details for the client to calculate and display the discount.
      security:
        - bearerAuth: []
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Coupon is valid.
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                  discount_type:
                    type: string
                  value:
                    type: integer
                  description:
                    type: string
        '404':
          description: Invalid coupon code.
        '410':
          description: Coupon has expired or reached its usage limit.
  /coupons/apply/{code}:
    post:
      tags:
        - Campaigns & Marketing
      summary: Apply a coupon to an account
      description: |-
        Applies a coupon to a user or team account.
        - For `credits` type coupons, it directly adds credits to the target and increments the coupon's use count.
        - For `percentage` or `fixed_amount` coupons, it validates the coupon and returns its details, intended to be used in a subsequent payment processing step.
      security:
        - bearerAuth: []
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                target_type:
                  type: string
                  enum: [user, team]
                  default: user
                target_id:
                  type: integer
                  description: Required if `target_type` is 'team'.
      responses:
        '200':
          description: Coupon applied or validated successfully.
        '403':
          description: Only team owners can apply coupons to teams.
        '404':
          description: Invalid coupon code.
        '410':
          description: Coupon has expired or reached its usage limit.
  /payments/paddle-webhook:
    post:
      tags:
        - Payments
      summary: Handle Paddle payment webhooks
      description: An endpoint to receive and process incoming webhooks from Paddle for events like `subscription_payment_succeeded`. It logs the payment and can trigger other actions, such as adding credits to a user or team account.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: The webhook payload from Paddle.
      responses:
        '200':
          description: Webhook received and processed successfully.
  /support/tickets:
    get:
      tags:
        - Support
      summary: List user's support tickets
      description: Retrieves a list of all support tickets created by the authenticated user.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of ticket objects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ticket'
    post:
      tags:
        - Support
      summary: Create a new support ticket
      description: Creates a new support ticket for the authenticated user.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - subject
              properties:
                subject:
                  type: string
      responses:
        '201':
          description: Ticket created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ticket'
  /support/tickets/{ticket_id}:
    get:
      tags:
        - Support
      summary: Get a specific support ticket
      description: Retrieves the details and all messages for a specific support ticket. Only the ticket owner or a support/admin user can view it.
      security:
        - bearerAuth: []
      parameters:
        - name: ticket_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Ticket details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ticket'
        '403':
          description: Access denied.
        '404':
          description: Ticket not found.
  /support/tickets/{ticket_id}/messages:
    post:
      tags:
        - Support
      summary: Reply to a support ticket
      description: Adds a new message to a support ticket. Can be used by the ticket owner or a support/admin user.
      security:
        - bearerAuth: []
      parameters:
        - name: ticket_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - message
              properties:
                message:
                  type: string
      responses:
        '201':
          description: Message added successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TicketMessage'
        '403':
          description: Access denied.
        '404':
          description: Ticket not found.
  /admin/users:
    get:
      tags:
        - Admin
      summary: List all users
      description: Retrieves a paginated list of all users in the system. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: A paginated list of users.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Super Admin' role.
    post:
      tags:
        - Admin
      summary: Create a user
      description: Creates a new user. This action is audit-logged. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '201':
          description: User created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Super Admin' role.
  /admin/users/{user_id}:
    get:
      tags:
        - Admin
      summary: Get a specific user
      description: Retrieves details for a single user by their ID. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Super Admin' role.
        '404':
          description: User not found.
    put:
      tags:
        - Admin
      summary: Update a user
      description: Updates a user's profile information, credits, and API limits. This action is audit-logged. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: User updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Super Admin' role.
        '404':
          description: User not found.
  /admin/users/{user_id}/roles:
    post:
      tags:
        - Admin
      summary: Assign a role to a user
      description: Assigns an existing role to a user. This action is audit-logged. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_name
              properties:
                role_name:
                  type: string
      responses:
        '200':
          description: Role assigned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Super Admin' role.
        '404':
          description: User or Role not found.
  /admin/teams:
    get:
      tags:
        - Admin
      summary: List all teams
      description: Retrieves a paginated list of all teams in the system. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: A paginated list of teams.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/Team'
        '403':
          description: Requires 'Super Admin' role.
  /admin/system-settings:
    get:
      tags:
        - Admin
      summary: List all system settings
      description: Retrieves all system-wide settings, including alert thresholds. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of system settings.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemSetting'
        '403':
          description: Requires 'Super Admin' role.
    post:
      tags:
        - Admin
      summary: Create or update a system setting
      description: Creates a new system setting or updates an existing one. This action is audit-logged. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SystemSetting'
      responses:
        '200':
          description: Setting created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemSetting'
        '403':
          description: Requires 'Super Admin' role.
  /admin/audit-logs:
    get:
      tags:
        - Admin
      summary: List all audit logs
      description: Retrieves a paginated list of all audit logs in the system, ordered by most recent. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: A paginated list of audit logs.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/AuditLog'
        '403':
          description: Requires 'Super Admin' role.
  /admin/developer/accounts:
    get:
      tags:
        - Developer Admin
      summary: List developer accounts
      description: Retrieves a list of all users with 'Developer' or 'Developer Admin' roles. Requires 'Developer Admin' or 'Super Admin' role.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of developer users.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
        '403':
          description: Requires 'Developer Admin' or 'Super Admin' role.
  /admin/developer/resource-allocation:
    get:
      tags:
        - Developer Admin
      summary: Get resource allocation for a user or team
      description: Retrieves the current resource limits (API quota, rate limit, storage) and usage for a specific user or team. Requires 'Developer Admin' or 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: user_id
          in: query
          schema:
            type: integer
        - name: team_id
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: Resource limits and usage data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  resource_limits:
                    type: object
                  storage_used:
                    type: object
        '400':
          description: user_id or team_id is required.
        '403':
          description: Requires 'Developer Admin' or 'Super Admin' role.
        '404':
          description: User or Team not found.
    post:
      tags:
        - Developer Admin
      summary: Allocate resources to a user or team
      description: Sets the API quota, rate limit, and storage limit for a user or team. This action is audit-logged. Requires 'Developer Admin' or 'Super Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: integer
                team_id:
                  type: integer
                api_quota_per_month:
                  type: integer
                api_rate_limit_per_second:
                  type: integer
                storage_limit:
                  type: integer
      responses:
        '200':
          description: Resources allocated successfully.
        '403':
          description: Requires 'Developer Admin' or 'Super Admin' role.
        '404':
          description: User or Team not found.
  /admin/campaigns:
    post:
      tags:
        - Campaigns & Marketing
      summary: Create a campaign
      description: Creates a new marketing or sales campaign. This action is audit-logged. Requires 'Marketing' or 'Super Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Campaign'
      responses:
        '201':
          description: Campaign created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          description: Missing required fields.
        '403':
          description: Requires 'Marketing' or 'Super Admin' role.
        '409':
          description: Campaign name already exists.
  /admin/campaigns/{campaign_id}/performance:
    get:
      tags:
        - Campaigns & Marketing
      summary: Get campaign performance
      description: Retrieves performance metrics for a specific campaign, including signups, referrals, coupon usage, and revenue. Requires 'Marketing' or 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: campaign_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Campaign performance metrics.
          content:
            application/json:
              schema:
                type: object
                properties:
                  campaign:
                    $ref: '#/components/schemas/Campaign'
                  metrics:
                    type: object
                  referrals:
                    type: array
                    items:
                      $ref: '#/components/schemas/Referral'
        '403':
          description: Requires 'Marketing' or 'Super Admin' role.
        '404':
          description: Campaign not found.
  /admin/notifications:
    get:
      tags:
        - Notifications
      summary: List admin notifications
      description: Retrieves in-app notifications for the current admin user. Can be filtered by `unread` status or `severity`. Requires 'Super Admin' or 'Developer Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: unread
          in: query
          schema:
            type: string
            enum: ['true']
        - name: severity
          in: query
          schema:
            type: string
            enum: [info, warning, critical]
      responses:
        '200':
          description: A list of notifications.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
        '403':
          description: Admin access required.
  /admin/notifications/mark-read:
    post:
      tags:
        - Notifications
      summary: Mark notifications as read
      description: Marks a list of notifications as read by their IDs. This action is audit-logged. Requires 'Super Admin' or 'Developer Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - ids
              properties:
                ids:
                  type: array
                  items:
                    type: integer
      responses:
        '200':
          description: Notifications marked as read.
        '400':
          description: No notification IDs provided.
        '403':
          description: Admin access required.
  /admin/notifications/dismiss:
    post:
      tags:
        - Notifications
      summary: Dismiss notifications
      description: Marks a list of notifications as dismissed by their IDs. This action is audit-logged. Requires 'Super Admin' or 'Developer Admin' role.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - ids
              properties:
                ids:
                  type: array
                  items:
                    type: integer
      responses:
        '200':
          description: Notifications dismissed.
        '400':
          description: No notification IDs provided.
        '403':
          description: Admin access required.
  /analytics/dashboard/overview:
    get:
      tags:
        - Analytics
      summary: Get dashboard overview
      description: Retrieves high-level overview statistics for the admin dashboard, covering users, teams, OTPs, API usage, support, revenue, and credits. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: Overview statistics.
        '403':
          description: Requires 'Super Admin' role.
  /analytics/dashboard/user-growth:
    get:
      tags:
        - Analytics
      summary: Get user growth data
      description: Retrieves user growth data over time, aggregated by day, week, or month depending on the selected period. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: Time-series data for new user signups.
        '403':
          description: Requires 'Super Admin' role.
  /analytics/dashboard/api-usage:
    get:
      tags:
        - Analytics
      summary: Get API usage statistics
      description: Retrieves statistics on API key usage, including active keys, recently used keys, and top users by key count. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: API usage statistics.
        '403':
          description: Requires 'Super Admin' role.
  /analytics/dashboard/security:
    get:
      tags:
        - Analytics
      summary: Get security statistics
      description: Retrieves security-related statistics, including secondary password adoption rate, failed login attempts, and locked user counts. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: Security-related statistics.
        '403':
          description: Requires 'Super Admin' role.
  /analytics/sales/overview:
    get:
      tags:
        - Analytics
      summary: Get sales overview
      description: Retrieves sales-focused analytics, including revenue, coupon usage, and customer acquisition metrics. Requires 'Sales' or 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: Sales overview analytics.
        '403':
          description: Requires 'Sales' or 'Super Admin' role.
  /analytics/marketing/overview:
    get:
      tags:
        - Analytics
      summary: Get marketing overview
      description: Retrieves marketing-focused analytics, including invitation metrics and user acquisition channel breakdowns. Requires 'Marketing' or 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: Marketing overview analytics.
        '403':
          description: Requires 'Marketing' or 'Super Admin' role.
  /analytics/reports/system-health:
    get:
      tags:
        - Analytics
      summary: Get system health report
      description: Retrieves real-time system health and performance metrics, including database connection pool stats, Celery queue length, Redis memory usage, and webhook health. Requires 'Super Admin' role.
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [today, week, month, quarter, year]
            default: month
      responses:
        '200':
          description: System health metrics.
        '403':
          description: Requires 'Super Admin' role.