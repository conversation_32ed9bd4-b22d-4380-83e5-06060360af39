# Project: Cloud-OTP
> `Cloud-OTP` is the project name not the application or the platform name.

The cloud-otp application is a comprehensive, multi-tenant, secure OTP (One-Time Password) management platform. It is built with Flask and designed for both individual and enterprise use, featuring extensive capabilities for team collaboration, security, automation, and administration.

### Table of Contents

- [Core Concepts](#core-concepts)
- [Detailed Feature Analysis](#detailed-feature-analysis)
	- [User & Authentication Management](#user--authentication-management)
	- [OTP & Vault Management](#otp--vault-management)
	- [Team & Collaboration Features](#team--collaboration-features)
	- [Security & Encryption](#security--encryption)
	- [API, Automation & Rate Limiting](#api-automation--rate-limiting)
	- [Administration & Role-Based Access Control (RBAC)](#administration--role-based-access-control-rbac)
	- [Billing, Sales & Marketing](#billing-sales--marketing)
	- [Analytics & Reporting](#analytics--reporting)
	- [System & Operations](#system--operations)
	- [Compliance (GDPR/CCPA)](#compliance-gdprccpa)
- [Technical Architecture & Stack](#technical-architecture--stack)
	- [Technology Stack](#technology-stack)
	- [Database Schema](#database-schema)
	- [API Endpoint Structure](#api-endpoint-structure)

---

## 1. Core Concepts

The application, **Cloud OTP**, is a backend service for a secure, cloud-based OTP management platform. Its primary function is to store, manage, and share OTP secrets for users and teams.

- **User:** The fundamental entity. A user can be an individual or a member of one or more teams.
- **Team:** A collaborative workspace where multiple users can share access to OTPs. Teams have owners, admins, and members with different permission levels.
- **OTP:** The core data object, representing a one-time password secret (e.g., from a QR code). It is always stored encrypted.
- **Vault:** A user's or team's collection of OTPs, organized into folders and supplemented with secure notes. The entire vault is designed to be end-to-end encrypted using a user-provided **Secondary Password**.
- **Zero-Knowledge Encryption:** The architecture is built around a zero-knowledge principle. The server stores an encrypted_dek (Data Encryption Key) for each user. This DEK is itself encrypted with a key derived from the user's **Secondary Password**. The server never sees the plaintext secondary password or the plaintext DEK, ensuring that the server administrators cannot decrypt user vault data (OTP secrets, notes).
- **Multi-tenancy:** The system is designed to serve multiple users and teams, with clear data separation and ownership.
- **Role-Based Access Control (RBAC):** The application defines multiple administrative roles (Super Admin, Developer Admin, Sales, Marketing, Support) to grant granular access to different parts of the system's administrative dashboards.

## 2. Detailed Feature Analysis

### User & Authentication Management

- **Local Authentication:** Standard email and password registration (/auth/register) and login (/auth/login). Passwords are hashed using Bcrypt.
- **Social/OAuth2 Logins:** Integrated with **Authlib** to support multiple OAuth providers: Google, GitHub, Microsoft, LinkedIn, and Facebook. It can also be configured for a generic OIDC provider. The system handles user provisioning on the first social login. (app/sso.py, app/routes/auth.py)
- **Enterprise SSO (SAML 2.0):** Provides per-team SAML-based Single Sign-On. Team admins can configure their Identity Provider (IdP) details (Entity ID, SSO/SLO URLs, x509 certs). The system acts as a Service Provider (SP), handling AuthnRequests and consuming SAML assertions to log in and provision users. It supports IdP-initiated login and SP-initiated login/logout. (app/routes/saml.py)
- **User Profile:** Users have a profile with basic information, credits, and API quota settings. (app/models.py:User)

### OTP & Vault Management

- **OTP CRUD:** Full Create, Read, Update, and Delete operations for OTP entries. Secrets are expected to be provided in an encrypted format (secret_encrypted). (app/routes/otp.py)
- **Folder Organization:** Users and teams can create nested folders to organize their OTPs. (app/routes/otp.py, app/models.py:Folder)
- **Secure Notes:** Ability to attach encrypted, titled notes to each OTP entry for additional context. (app/routes/otp.py, app/models.py:Note)
- **Vault Import/Export:** Users can export their entire vault (OTPs, folders, notes) as a single encrypted JSON blob, protected by their secondary password. They can also import this blob to restore their vault, which replaces existing data. (app/routes/auth.py)

### Team & Collaboration Features

- **Team Management:** Users can create teams, making them the owner. Owners can manage team settings and members. (app/routes/teams.py)
- **Member Invitations:** Team admins can invite new members via email. The system generates a unique, expiring token and sends an invitation email (via a Celery task). (app/routes/teams.py, app/tasks.py)
- **Member Roles:** Team members can have roles like admin, member, or read-only, granting different levels of access within the team. (app/models.py:TeamMember)
- **OTP Sharing:**
	- **Direct User Share:** Share a single OTP with another user with read, write, or admin permissions.
	- **Folder Sharing:** Share an entire folder (and all its contents, including sub-folders) with other users or even entire teams.
	- **Public Link Sharing:** Create a public, shareable link for an OTP, which can be password-protected and have an access limit.
	- All sharing actions can have an expiration time. (app/routes/otp.py)
- **Team Audit Logs:** All significant actions within a team are logged and can be reviewed by team admins, with filtering capabilities. (app/routes/teams.py)
- **Team Resource Management:** Admins can set resource limits (e.g., number of OTPs, API keys) for their teams. (app/routes/teams.py, app/models.py:TeamResourceAllocation)

### Security & Encryption

- **End-to-End Encryption Principle:** The use of a secondary_password and an encrypted_dek ensures that sensitive vault data is encrypted in a way that the server cannot access it. The crypto_utils.py file implements the key derivation (PBKDF2-HMAC-SHA256 with 1.2M iterations) and encryption (Fernet).
- **Secondary Password with Lockout:** To protect against brute-force attacks on the secondary password, the system implements a sophisticated lockout mechanism. It tracks failed attempts and enforces an exponentially increasing lockout duration, culminating in a permanent lockout after 11 failures. (app/routes/secondary_password.py)
- **API Key Security:** API keys are generated with a prefix and are not stored in plaintext. Only a SHA-256 hash of the key is stored in the database. (app/models.py:APIKey)
- **Role-Based Access Control (RBAC):** Granular control over application features is managed through decorators (@admin_required, @sales_required, etc.) that check a user's assigned roles. (app/routes/admin_helpers.py)
- **Audit Logging:** Comprehensive audit logs track all critical actions performed by users and admins, including IP address, user agent, and endpoint details. (app/models.py:AuditLog, app/routes/admin_helpers.py)

### API, Automation & Rate Limiting

- **RESTful API:** A well-structured API for all major features, documented via an OpenAPI-like JSON spec available at /api/docs. (app/api_docs.py)
- **API Key Authentication:** Users and teams can generate API keys for programmatic access.
- **Rate Limiting & Quotas:** A robust system using Redis to enforce per-second rate limits and monthly API request quotas for each user and team. Exceeding limits returns a 429 Too Many Requests error. (app/routes/rate_limiting.py)
- **Webhooks:** A powerful webhook system allows users/teams to subscribe to events (otp.shared, user.registered, payment.received, etc.).
	- **Configuration:** Users/admins can configure webhook endpoints, secrets for payload signing, and specific events to subscribe to.
	- **Delivery:** Webhook delivery is handled asynchronously by Celery, with automatic retries and exponential backoff for failed deliveries.
	- **Monitoring:** Admins can view the status of all webhook events, retry failed ones, and see delivery statistics. (app/routes/webhooks.py, app/tasks.py)

### Administration & Role-Based Access Control (RBAC)

- **Multi-faceted Admin Dashboards:** The API provides endpoints for several distinct admin dashboards:
	- **Super Admin:** Full control over users, teams, system settings, and audit logs.
	- **Developer Admin:** Manages developer accounts and allocates resources (API quotas, storage limits) to users and teams.
	- **Sales Admin:** Manages coupons and views sales analytics.
	- **Marketing Admin:** Manages invitations and marketing campaigns.
	- **Support Admin:** Manages all support tickets.
- **User & Team Management:** Admins can perform full CRUD operations on users and teams, assign roles, and update resource limits. (app/routes/admin.py)
- **System Settings:** Admins can configure system-wide settings, including alert thresholds for system health monitoring. (app/routes/admin.py)
- **In-App Notifications:** The system can generate in-app notifications for admins (e.g., system alerts for high error rates, low credits). Admins can list, mark as read, and dismiss these notifications. (app/routes/admin.py, app/tasks.py)

### Billing, Sales & Marketing

- **Payment Processing:** Integrated with **Paddle** for payment processing. It uses a webhook (/payments/paddle-webhook) to listen for payment events like subscription_payment_succeeded. (app/routes/payments.py)
- **Coupons:** The sales team can create, manage, and track usage of coupon codes. Coupons can provide discounts or add credits to user/team accounts. (app/routes/coupons.py)
- **Invitations:** The marketing team can create and manage platform invitation codes. (app/routes/invitations.py)
- **Campaign Management:** A full-featured campaign system allows marketing/sales to create campaigns, track their performance (signups, referrals, revenue, coupon usage), and attribute user acquisition to specific campaigns. (app/routes/admin.py, app/models.py:Campaign, app/models.py:Referral)

### Analytics & Reporting

- **Analytics Service:** A dedicated AnalyticsService class encapsulates the logic for generating complex reports. (app/services/analytics_service.py)
- **Comprehensive Dashboards:** The API provides data for various analytics dashboards:
	- **Overview:** High-level metrics on users, teams, OTPs, API usage, support, and revenue.
	- **User Growth:** Time-series data on new user signups.
	- **API Usage:** Statistics on API key usage and top users.
	- **Security:** Metrics on secondary password adoption, failed logins, and account lockouts.
	- **Sales & Marketing:** Overviews of revenue, coupon usage, and campaign performance.
- **System Health Monitoring:** An endpoint provides critical system health metrics, including database connection pool stats, Celery queue length, and Redis memory usage. (/analytics/reports/system-health)
- **Data Export:** Admins can export user data for further analysis. (/analytics/export/users)

### System & Operations

- **Technology Stack:** Python 3.12, Flask, SQLAlchemy, PostgreSQL (or SQLite), Redis, Celery.
- **Configuration:** Uses .env files and environment variables for flexible configuration across different environments (Dev, Test, Prod). (app/config.py)
- **Database Migrations:** Uses Alembic for managing database schema changes. (migrations/)
- **Background Tasks:** Uses Celery for handling long-running or asynchronous tasks like sending emails and webhooks, and running periodic jobs (e.g., system alert checks). (app/tasks.py)
- **CLI:** Provides a set of Flask CLI commands for administrative tasks like creating admin users, initializing the database, adding credits, and viewing system stats. (app/cli.py)
- **Containerization:** Includes a Dockerfile for building a production-ready container image and a gunicorn.conf.py for running the application.
- **Health Checks:** Provides /healthcheck and /healthz endpoints for load balancers and container orchestration systems (like Kubernetes). (app/extensions.py)
- **Monitoring:** Integrated with Sentry for error tracking and Prometheus for metrics. (app/__init__.py, app/extensions.py)

### Compliance (GDPR/CCPA)

- **Data Export:** A dedicated endpoint (/users/me/export-data) allows users to download all of their personal data in a machine-readable JSON format, fulfilling the "right to data portability".
- **Account Deletion:** A dedicated endpoint (/users/me/request-account-deletion) allows users to request immediate and irreversible deletion of their account and all associated data, fulfilling the "right to be forgotten". This process is thorough, deleting records from over a dozen related tables.