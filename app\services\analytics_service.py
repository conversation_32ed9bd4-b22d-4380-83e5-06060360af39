import pandas as pd
from sqlalchemy import func, and_, or_, case
from datetime import datetime, timedelta
from flask import current_app
from ..extensions import db
from ..models import (
    User, Team, OTP, APIKey, Coupon, Invitation, AuditLog, 
    Ticket, Payment, OTPShare, SecondaryPasswordAttempt, WebhookEvent, Campaign, Referral
)
from celery import current_app as celery_app
import redis

class AnalyticsService:
    @staticmethod
    def get_dashboard_overview(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        end_date = filters.get("end_date")
        # User statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        new_users = User.query.filter(User.created_at >= start_date).count()
        # Team statistics
        total_teams = Team.query.count()
        new_teams = Team.query.filter(Team.created_at >= start_date).count()
        # OTP statistics
        total_otps = OTP.query.count()
        new_otps = OTP.query.filter(OTP.created_at >= start_date).count()
        shared_otps = OTPShare.query.filter_by(is_active=True).count()
        # API usage
        total_api_keys = APIKey.query.filter_by(is_active=True).count()
        api_keys_used_recently = APIKey.query.filter(
            APIKey.last_used_at >= start_date
        ).count()
        # Support tickets
        total_tickets = Ticket.query.count()
        open_tickets = Ticket.query.filter_by(status="open").count()
        new_tickets = Ticket.query.filter(Ticket.created_at >= start_date).count()
        # Revenue (from payments)
        total_revenue = db.session.query(func.sum(Payment.amount)).scalar() or 0
        period_revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.created_at >= start_date
        ).scalar() or 0
        # Credits distributed
        total_credits = db.session.query(func.sum(User.credits)).scalar() or 0
        team_credits = db.session.query(func.sum(Team.credits)).scalar() or 0
        return {
            "period": period,
            "users": {
                "total": total_users,
                "active": active_users,
                "new": new_users,
                "growth_rate": round((new_users / total_users) * 100, 2) if total_users > 0 else 0
            },
            "teams": {
                "total": total_teams,
                "new": new_teams
            },
            "otps": {
                "total": total_otps,
                "new": new_otps,
                "shared": shared_otps
            },
            "api": {
                "total_keys": total_api_keys,
                "recently_used": api_keys_used_recently
            },
            "support": {
                "total_tickets": total_tickets,
                "open_tickets": open_tickets,
                "new_tickets": new_tickets
            },
            "revenue": {
                "total": total_revenue,
                "period": period_revenue
            },
            "credits": {
                "user_credits": total_credits,
                "team_credits": team_credits,
                "total_credits": total_credits + team_credits
            }
        }

    @staticmethod
    def get_user_growth(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Group users by day/week/month based on period
        if period in ["today", "week"]:
            date_trunc = func.date(User.created_at)
        elif period == "month":
            date_trunc = func.date_trunc('week', User.created_at)
        else:
            date_trunc = func.date_trunc('month', User.created_at)
        growth_data = db.session.query(
            date_trunc.label('date'),
            func.count(User.id).label('new_users')
        ).filter(
            User.created_at >= start_date
        ).group_by(date_trunc).order_by(date_trunc).all()
        return {
            "period": period,
            "data": [
                {
                    "date": row.date.isoformat() if row.date else None,
                    "new_users": row.new_users
                }
                for row in growth_data
            ]
        }

    @staticmethod
    def get_api_usage(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # API key usage
        active_keys = APIKey.query.filter_by(is_active=True).count()
        recently_used = APIKey.query.filter(
            APIKey.last_used_at >= start_date
        ).count()
        # Top API key users
        top_users = db.session.query(
            User.email,
            func.count(APIKey.id).label('key_count')
        ).join(APIKey).filter(
            APIKey.is_active == True
        ).group_by(User.id, User.email).order_by(
            func.count(APIKey.id).desc()
        ).limit(10).all()
        return {
            "period": period,
            "active_keys": active_keys,
            "recently_used": recently_used,
            "usage_rate": round((recently_used / active_keys) * 100, 2) if active_keys > 0 else 0,
            "top_users": [
                {"email": user.email, "key_count": user.key_count}
                for user in top_users
            ]
        }

    @staticmethod
    def get_security_stats(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Secondary password adoption
        users_with_secondary = User.query.filter(
            User.secondary_password_hash.isnot(None)
        ).count()
        total_users = User.query.count()
        # Failed login attempts
        failed_attempts = SecondaryPasswordAttempt.query.filter(
            SecondaryPasswordAttempt.success == False,
            SecondaryPasswordAttempt.created_at >= start_date
        ).count()
        successful_attempts = SecondaryPasswordAttempt.query.filter(
            SecondaryPasswordAttempt.success == True,
            SecondaryPasswordAttempt.created_at >= start_date
        ).count()
        # Locked users
        locked_users = db.session.query(
            SecondaryPasswordAttempt.user_id,
            func.count(SecondaryPasswordAttempt.id).label('failed_count')
        ).filter(
            SecondaryPasswordAttempt.success == False,
            SecondaryPasswordAttempt.created_at >= start_date
        ).group_by(SecondaryPasswordAttempt.user_id).having(
            func.count(SecondaryPasswordAttempt.id) >= 5
        ).count()
        # Audit log activity
        audit_activity = AuditLog.query.filter(
            AuditLog.created_at >= start_date
        ).count()
        return {
            "period": period,
            "secondary_password_adoption": {
                "users_with_secondary": users_with_secondary,
                "total_users": total_users,
                "adoption_rate": round((users_with_secondary / total_users) * 100, 2) if total_users > 0 else 0
            },
            "authentication": {
                "failed_attempts": failed_attempts,
                "successful_attempts": successful_attempts,
                "success_rate": round((successful_attempts / (successful_attempts + failed_attempts)) * 100, 2) if (successful_attempts + failed_attempts) > 0 else 0
            },
            "locked_users": locked_users,
            "audit_activity": audit_activity
        }

    @staticmethod
    def get_sales_overview(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Revenue metrics
        total_revenue = db.session.query(func.sum(Payment.amount)).scalar() or 0
        period_revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.created_at >= start_date
        ).scalar() or 0
        # Coupon usage
        coupons_used = db.session.query(func.sum(Coupon.use_count)).scalar() or 0
        period_coupons = db.session.query(func.sum(Coupon.use_count)).filter(
            Coupon.updated_at >= start_date
        ).scalar() or 0
        # Credits distributed via coupons
        credits_distributed = db.session.query(
            func.sum(Coupon.value * Coupon.use_count)
        ).filter(Coupon.discount_type == "credits").scalar() or 0
        # Customer acquisition
        new_customers = User.query.filter(User.created_at >= start_date).count()
        paying_customers = db.session.query(Payment.user_id).distinct().count()
        # Campaign breakdown
        campaign_stats = []
        for campaign in Campaign.query.all():
            signups = User.query.filter_by(campaign_id=campaign.id).count()
            revenue = db.session.query(func.sum(Payment.amount)).join(User, Payment.user_id == User.id).filter(User.campaign_id == campaign.id).scalar() or 0
            coupon_uses = db.session.query(func.sum(Coupon.use_count)).filter(Coupon.campaign_id == campaign.id).scalar() or 0
            campaign_stats.append({
                "campaign": {
                    "id": campaign.id,
                    "name": campaign.name,
                    "type": campaign.type,
                    "description": campaign.description,
                    "start_date": campaign.start_date.isoformat() if campaign.start_date else None,
                    "end_date": campaign.end_date.isoformat() if campaign.end_date else None,
                },
                "signups": signups,
                "revenue": revenue,
                "coupon_uses": coupon_uses,
            })
        return {
            "period": period,
            "revenue": {
                "total": total_revenue,
                "period": period_revenue
            },
            "coupons": {
                "total_used": coupons_used,
                "period_used": period_coupons,
                "credits_distributed": credits_distributed
            },
            "customers": {
                "new": new_customers,
                "paying": paying_customers,
                "conversion_rate": round((paying_customers / new_customers) * 100, 2) if new_customers > 0 else 0
            },
            "campaigns": campaign_stats
        }

    @staticmethod
    def get_marketing_overview(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Invitation metrics
        total_invitations = Invitation.query.count()
        active_invitations = Invitation.query.filter_by(is_active=True).count()
        period_invitations = Invitation.query.filter(
            Invitation.created_at >= start_date
        ).count()
        # User acquisition channels
        organic_signups = User.query.filter(User.created_at >= start_date, User.campaign_id == None).count()
        campaign_signups = db.session.query(User.campaign_id, func.count(User.id)).filter(User.created_at >= start_date, User.campaign_id != None).group_by(User.campaign_id).all()
        campaign_breakdown = []
        for campaign_id, count in campaign_signups:
            campaign = Campaign.query.get(campaign_id)
            campaign_breakdown.append({
                "campaign": {
                    "id": campaign.id,
                    "name": campaign.name,
                    "type": campaign.type,
                    "description": campaign.description,
                    "start_date": campaign.start_date.isoformat() if campaign.start_date else None,
                    "end_date": campaign.end_date.isoformat() if campaign.end_date else None,
                },
                "signups": count
            })
        # Referral metrics
        total_referrals = Referral.query.count()
        period_referrals = Referral.query.filter(Referral.created_at >= start_date).count()
        # Team growth
        new_teams = Team.query.filter(Team.created_at >= start_date).count()
        return {
            "period": period,
            "invitations": {
                "total": total_invitations,
                "active": active_invitations,
                "period": period_invitations
            },
            "acquisition": {
                "organic_signups": organic_signups,
                "campaigns": campaign_breakdown,
                "referrals": {
                    "total": total_referrals,
                    "period": period_referrals
                },
                "new_teams": new_teams
            }
        }

    @staticmethod
    def get_user_activity_report(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Most active users (by OTP creation)
        active_users = db.session.query(
            User.email,
            func.count(OTP.id).label('otp_count'),
            func.max(OTP.created_at).label('last_activity')
        ).join(OTP).filter(
            OTP.created_at >= start_date
        ).group_by(User.id, User.email).order_by(
            func.count(OTP.id).desc()
        ).limit(20).all()
        # Users with most API usage
        api_users = db.session.query(
            User.email,
            func.count(APIKey.id).label('api_key_count'),
            func.max(APIKey.last_used_at).label('last_api_use')
        ).join(APIKey).filter(
            APIKey.is_active == True
        ).group_by(User.id, User.email).order_by(
            func.count(APIKey.id).desc()
        ).limit(20).all()
        return {
            "period": period,
            "most_active_users": [
                {
                    "email": user.email,
                    "otp_count": user.otp_count,
                    "last_activity": user.last_activity.isoformat() + "Z" if user.last_activity else None
                }
                for user in active_users
            ],
            "top_api_users": [
                {
                    "email": user.email,
                    "api_key_count": user.api_key_count,
                    "last_api_use": user.last_api_use.isoformat() + "Z" if user.last_api_use else None
                }
                for user in api_users
            ]
        }

    @staticmethod
    def get_system_health(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        # Database statistics
        total_records = (
            User.query.count() +
            Team.query.count() +
            OTP.query.count() +
            APIKey.query.count()
        )
        # Recent activity (last 24 hours)
        recent_cutoff = datetime.utcnow() - timedelta(hours=24)
        recent_activity = AuditLog.query.filter(
            AuditLog.created_at >= recent_cutoff
        ).count()
        # Webhook health
        pending_webhooks = WebhookEvent.query.filter_by(status="pending").count()
        failed_webhooks = WebhookEvent.query.filter_by(status="failed").count()
        # Error rates
        recent_errors = AuditLog.query.filter(
            AuditLog.created_at >= recent_cutoff,
            AuditLog.action.like('%error%')
        ).count()

        # --- DB Connection Pool Stats ---
        pool_stats = {}
        try:
            engine = db.get_engine(current_app)
            pool = engine.pool
            pool_stats = {
                "checked_in": getattr(pool, 'checkedin', lambda: None)(),
                "checked_out": getattr(pool, 'checkedout', lambda: None)(),
                "overflow": getattr(pool, 'overflow', lambda: None)(),
                "current_size": getattr(pool, 'size', lambda: None)(),
                "max_overflow": getattr(pool, 'max_overflow', lambda: None)(),
                "pool_timeout": getattr(pool, 'timeout', None),
            }
        except Exception as e:
            pool_stats = {"error": str(e)}

        # --- Celery Queue Length ---
        celery_queue_length = None
        try:
            redis_url = current_app.config.get("CELERY_BROKER_URL")
            r = redis.Redis.from_url(redis_url)
            celery_queue_name = current_app.config.get("CELERY_DEFAULT_QUEUE", "celery")
            celery_queue_length = r.llen(celery_queue_name)
        except Exception as e:
            celery_queue_length = f"error: {e}"

        # --- Redis Memory Usage ---
        redis_memory = {}
        try:
            redis_url = current_app.config.get("REDIS_URL") or current_app.config.get("CELERY_BROKER_URL")
            r = redis.Redis.from_url(redis_url)
            mem_info = r.info("memory")
            redis_memory = {
                "used_memory": mem_info.get("used_memory"),
                "used_memory_human": mem_info.get("used_memory_human"),
                "used_memory_rss": mem_info.get("used_memory_rss"),
                "mem_fragmentation_ratio": mem_info.get("mem_fragmentation_ratio"),
                "maxmemory": mem_info.get("maxmemory"),
                "maxmemory_human": mem_info.get("maxmemory_human"),
            }
        except Exception as e:
            redis_memory = {"error": str(e)}

        return {
            "database": {
                "total_records": total_records,
                "recent_activity": recent_activity,
                "pool_stats": pool_stats
            },
            "webhooks": {
                "pending": pending_webhooks,
                "failed": failed_webhooks,
                "health_status": "healthy" if pending_webhooks < 100 and failed_webhooks < 50 else "warning"
            },
            "errors": {
                "recent_errors": recent_errors,
                "error_rate": round((recent_errors / recent_activity) * 100, 2) if recent_activity > 0 else 0
            },
            "celery": {
                "queue_length": celery_queue_length
            },
            "redis": redis_memory,
            "overall_health": "healthy" if recent_errors < 10 and pending_webhooks < 100 else "warning"
        }

    @staticmethod
    def export_users(filters):
        period = filters.get("period", "month")
        start_date = filters.get("start_date")
        users = User.query.filter(User.created_at >= start_date).all()
        user_data = []
        for user in users:
            user_data.append({
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "is_active": user.is_active,
                "credits": user.credits,
                "created_at": user.created_at.isoformat() + "Z",
                "has_secondary_password": bool(user.secondary_password_hash),
                "otp_count": len(user.otps),
                "api_key_count": len([k for k in user.api_keys if k.is_active])
            })
        return {
            "export_date": datetime.utcnow().isoformat() + "Z",
            "total_users": len(user_data),
            "users": user_data
        } 