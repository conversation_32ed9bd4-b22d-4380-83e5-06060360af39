# Cloud OTP Backend

Cloud OTP is a secure, multi-tenant, cloud-native backend for a One-Time Password (OTP) management platform. It is designed for both individual and enterprise use, with a strong emphasis on security, collaboration, and automation. The platform is built on a zero-knowledge encryption model, ensuring that even server administrators cannot access user vault data.

This repository contains the complete backend source code, built with Python, Flask, and a suite of powerful extensions.

## ✨ Features

### Security & Encryption

*   **Zero-Knowledge Architecture:** User vaults (OTP secrets, notes) are end-to-end encrypted. The server stores an encrypted Data Encryption Key (DEK) that can only be decrypted with the user's secondary password, which is never sent to the server.
*   **Strong Encryption:** Uses PBKDF2-HMAC-SHA256 with 1.2 million iterations for key derivation and AES-256 (via <PERSON><PERSON><PERSON>) for data encryption.
*   **Secondary Password with Lockout:** Brute-force protection on the vault password with an exponential backoff lockout mechanism.
*   **Role-Based Access Control (RBAC):** Granular permissions for system administrators (`Super Admin`, `Developer Admin`, `Sales`, `Marketing`, `Support`) and team members (`admin`, `member`, `read-only`).
*   **Comprehensive Audit Logging:** All critical actions by users and admins are logged for security and compliance.
*   **Secure API Keys:** API keys are hashed before storage (SHA-256) and are never stored in plaintext.

### User & Authentication

*   **Multiple Login Methods:**
    *   Standard Email/Password Registration.
    *   **OAuth 2.0:** Google, GitHub, Microsoft, LinkedIn, Facebook.
    *   **SAML 2.0:** Per-team enterprise Single Sign-On (SSO) integration.
*   **GDPR/CCPA Compliance:** Endpoints for users to export all their personal data and to request permanent, irreversible account deletion.

### Teams & Collaboration

*   **Team Workspaces:** Create teams to share and manage OTPs collaboratively.
*   **Granular Sharing:**
    *   Share individual OTPs with other users.
    *   Share entire folders (including sub-folders) with users or teams.
    *   Set `read`, `write`, or `admin` permissions on shares.
    *   Set expiration dates for shared access.
*   **Email-Based Invitations:** Team admins can invite new members via secure, expiring email links.
*   **Team Audit Logs & Resource Management:** Admins can monitor team activity and allocate resources.

### API & Automation

*   **Full-Featured REST API:** A comprehensive API to manage all platform resources.
*   **Webhooks:** A robust webhook system to receive real-time notifications for events like `otp.shared`, `user.registered`, `payment.received`, and `team.member_added`.
*   **Rate Limiting & Quotas:** Per-user and per-team API rate limiting and monthly usage quotas, managed via Redis.
*   **API Documentation:** Self-hosted Swagger UI for exploring and testing the API at the `/api/docs/swagger` endpoint.

### Administration & Analytics

*   **Multi-Faceted Admin Dashboards:** API endpoints to power dashboards for Super Admins, Developers, Sales, Marketing, and Support.
*   **Campaign Management:** Create and track the performance of marketing/sales campaigns, with attribution for signups, revenue, and referrals.
*   **Billing Integration:** Integrated with Paddle for subscription and payment processing via webhooks.
*   **Coupon & Invitation System:** Create and manage promotional coupons and user invitations.
*   **In-Depth Analytics:** Endpoints to provide data on user growth, API usage, security posture, sales funnels, and marketing campaign effectiveness.
*   **System Health Monitoring:** An endpoint (`/analytics/reports/system-health`) provides real-time metrics on the database, background jobs (Celery), and cache (Redis).

## 🛠️ Technical Stack

*   **Framework:** Flask
*   **Database:** SQLAlchemy ORM (compatible with PostgreSQL, SQLite)
*   **Authentication:** Flask-JWT-Extended, Authlib (OAuth), PySAML2 (SAML)
*   **Background Jobs:** Celery
*   **Caching & Messaging:** Redis
*   **API Serialization:** Flask-Marshmallow
*   **Deployment:** Docker, Gunicorn
*   **Monitoring:** Sentry, Prometheus
*   **Dependencies:** See `pyproject.toml` for the full list.

## 🚀 Getting Started

### Prerequisites

*   Python 3.12+
*   Docker (recommended)
*   A running PostgreSQL server
*   A running Redis instance

### 1. Configuration

1.  Copy the `.env.example` file to `.env` (this file is not included but is standard practice).
2.  Fill in the environment variables in the `.env` file. Key variables to set include:
    *   `FLASK_ENV`: `development`
    *   `SECRET_KEY`: A long, random string.
    *   `JWT_SECRET_KEY`: Another long, random string.
    *   `DATABASE_URL`: Your PostgreSQL connection string (e.g., `postgresql://user:password@localhost/cloud_otp`).
    *   `REDIS_URL`: Your Redis connection string (e.g., `redis://localhost:6379/0`).
    *   `CELERY_BROKER_URL` & `CELERY_RESULT_BACKEND`: Your Redis connection strings for Celery.

### 2. Installation & Setup

* Install uv python packages manager
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

* Clone the repository
```bash
<NAME_EMAIL>:skpassegna/backend-otpcloud.git
```

* Navigate to the project directory
```bash
cd cloud-otp
```

* Istall dependencies, create virtual environment and activate it

```bash
uv sync
```

* *Apply database migrations
```bash
flask db upgrade
```

* Initialize the database with default roles and settings
```bash
flask init-db
```

* Create your first admin user
```bash
flask create-admin <EMAIL>
```
_You will be prompted for a password._

### 3. Running the Application

You need to run the Flask/Gunicorn server and the Celery worker in separate terminals.

**Terminal 1: Run the Web Server**

```bash
gunicorn --config gunicorn.conf.py "main:app"
```


The application will be available at http://localhost:8000.

**Terminal 2: Run the Celery Worker**

```bash
celery -A main.celery worker -l info
```


## Command-Line Interface (CLI)

The application includes several helpful CLI commands for administration.

- flask init-db: Initializes the database with roles and system settings.
- flask reset-db: **(DANGER)** Drops all tables and recreates the schema.
- flask create-admin <email>: Creates a new user with the "Super Admin" role.
- flask create-test-data: Populates the database with sample users and teams.
- flask list-users: Lists users in the database.
- flask user-info <email>: Shows detailed information for a specific user.
- flask add-credits <email> <amount>: Adds credits to a user's account.
- flask system-stats: Displays high-level statistics about the platform.
- flask cleanup-expired: Deactivates expired coupons and invitations.

## API Documentation

Once the application is running, full API documentation is available via Swagger UI at:

**[http://localhost:8000/api/docs/swagger](http://localhost:8000/api/docs/swagger)**

A raw OpenAPI JSON specification is available at /api/docs.

## ⚖️ License

This project is under a **Private Proprietary License**. Please see the LICENSE file for more details. Unauthorized use, reproduction, or distribution is strictly prohibited. For licensing inquiries, please contact [<EMAIL>](mailto:<EMAIL>).