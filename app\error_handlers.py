"""Global error handlers for the Flask application."""
from __future__ import annotations

from http import HTT<PERSON>tatus
from typing import Any

from flask import Flask, jsonify, request
from werkzeug.exceptions import HTTPException
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from marshmallow import ValidationError
from flask_jwt_extended.exceptions import JWTExtendedException

from .extensions import db
from .models import AuditLog


def register_error_handlers(app: Flask) -> None:
    """Register global error handlers for the Flask application."""
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error: ValidationError) -> tuple[dict[str, Any], int]:
        """Handle Marshmallow validation errors."""
        return {
            "error": "Validation Error",
            "message": "The provided data is invalid",
            "details": error.messages
        }, HTTPStatus.BAD_REQUEST
    
    @app.errorhandler(IntegrityError)
    def handle_integrity_error(error: IntegrityError) -> tuple[dict[str, Any], int]:
        """Handle database integrity constraint violations."""
        db.session.rollback()
        
        # Log the error
        log_error("Database Integrity Error", str(error.orig))
        
        # Check for common constraint violations
        error_msg = str(error.orig).lower()
        
        if "unique constraint" in error_msg or "duplicate" in error_msg:
            if "email" in error_msg:
                message = "An account with this email already exists"
            elif "code" in error_msg:
                message = "This code is already in use"
            else:
                message = "This value already exists"
        elif "foreign key constraint" in error_msg:
            message = "Referenced resource does not exist"
        elif "not null constraint" in error_msg:
            message = "Required field is missing"
        else:
            message = "Database constraint violation"
        
        return {
            "error": "Constraint Violation",
            "message": message
        }, HTTPStatus.CONFLICT
    
    @app.errorhandler(SQLAlchemyError)
    def handle_database_error(error: SQLAlchemyError) -> tuple[dict[str, Any], int]:
        """Handle general database errors."""
        db.session.rollback()
        
        # Log the error
        log_error("Database Error", str(error))
        
        return {
            "error": "Database Error",
            "message": "A database error occurred. Please try again."
        }, HTTPStatus.INTERNAL_SERVER_ERROR
    
    @app.errorhandler(JWTExtendedException)
    def handle_jwt_error(error: JWTExtendedException) -> tuple[dict[str, Any], int]:
        """Handle JWT-related errors."""
        return {
            "error": "Authentication Error",
            "message": str(error)
        }, HTTPStatus.UNAUTHORIZED
    
    @app.errorhandler(HTTPException)
    def handle_http_error(error: HTTPException) -> tuple[dict[str, Any], int]:
        """Handle HTTP exceptions."""
        return {
            "error": error.name,
            "message": error.description
        }, error.code or HTTPStatus.INTERNAL_SERVER_ERROR
    
    @app.errorhandler(404)
    def handle_not_found(error: Any) -> tuple[dict[str, Any], int]:
        """Handle 404 Not Found errors."""
        return {
            "error": "Not Found",
            "message": "The requested resource was not found"
        }, HTTPStatus.NOT_FOUND
    
    @app.errorhandler(405)
    def handle_method_not_allowed(error: Any) -> tuple[dict[str, Any], int]:
        """Handle 405 Method Not Allowed errors."""
        return {
            "error": "Method Not Allowed",
            "message": f"The {request.method} method is not allowed for this endpoint"
        }, HTTPStatus.METHOD_NOT_ALLOWED
    
    @app.errorhandler(429)
    def handle_rate_limit_exceeded(error: Any) -> tuple[dict[str, Any], int]:
        """Handle 429 Too Many Requests errors."""
        return {
            "error": "Rate Limit Exceeded",
            "message": "Too many requests. Please slow down and try again later."
        }, HTTPStatus.TOO_MANY_REQUESTS
    
    @app.errorhandler(500)
    def handle_internal_server_error(error: Any) -> tuple[dict[str, Any], int]:
        """Handle 500 Internal Server Error."""
        # Log the error
        log_error("Internal Server Error", str(error))
        
        return {
            "error": "Internal Server Error",
            "message": "An unexpected error occurred. Please try again later."
        }, HTTPStatus.INTERNAL_SERVER_ERROR
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error: Exception) -> tuple[dict[str, Any], int]:
        """Handle any unexpected exceptions."""
        # Log the error
        log_error("Unexpected Error", str(error))
        
        # In production, don't expose internal error details
        if app.config.get("DEBUG"):
            return {
                "error": "Unexpected Error",
                "message": str(error),
                "type": type(error).__name__
            }, HTTPStatus.INTERNAL_SERVER_ERROR
        else:
            return {
                "error": "Internal Server Error",
                "message": "An unexpected error occurred. Please try again later."
            }, HTTPStatus.INTERNAL_SERVER_ERROR


def log_error(error_type: str, error_message: str) -> None:
    """Log an error to the audit log."""
    try:
        error_log = AuditLog(
            action=f"ERROR:{error_type}",
            details={
                "error_message": error_message,
                "endpoint": request.endpoint,
                "method": request.method,
                "url": request.url,
                "ip_address": request.remote_addr,
                "user_agent": request.headers.get("User-Agent")
            }
        )
        db.session.add(error_log)
        db.session.commit()
    except Exception:
        # If we can't log to database, at least print to console
        print(f"Error logging failed: {error_type} - {error_message}")


class APIException(Exception):
    """Custom API exception with status code and message."""
    
    def __init__(self, message: str, status_code: int = HTTPStatus.BAD_REQUEST, payload: dict[str, Any] | None = None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload
    
    def to_dict(self) -> dict[str, Any]:
        """Convert exception to dictionary for JSON response."""
        result = {"message": self.message}
        if self.payload:
            result.update(self.payload)
        return result


def register_api_exception_handler(app: Flask) -> None:
    """Register handler for custom API exceptions."""
    
    @app.errorhandler(APIException)
    def handle_api_exception(error: APIException) -> tuple[dict[str, Any], int]:
        """Handle custom API exceptions."""
        return {
            "error": "API Error",
            **error.to_dict()
        }, error.status_code


# Custom exception classes for specific scenarios
class AuthenticationError(APIException):
    """Authentication-related errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, HTTPStatus.UNAUTHORIZED)


class AuthorizationError(APIException):
    """Authorization-related errors."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, HTTPStatus.FORBIDDEN)


class ResourceNotFoundError(APIException):
    """Resource not found errors."""
    
    def __init__(self, resource: str = "Resource"):
        super().__init__(f"{resource} not found", HTTPStatus.NOT_FOUND)


class ValidationError(APIException):
    """Validation errors."""
    
    def __init__(self, message: str, details: dict[str, Any] | None = None):
        super().__init__(message, HTTPStatus.BAD_REQUEST, {"details": details} if details else None)


class RateLimitError(APIException):
    """Rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = 60):
        super().__init__(message, HTTPStatus.TOO_MANY_REQUESTS, {"retry_after": retry_after})


class QuotaExceededError(APIException):
    """API quota exceeded errors."""
    
    def __init__(self, used: int, limit: int):
        message = f"API quota exceeded. Used {used}/{limit} requests this month."
        super().__init__(message, HTTPStatus.TOO_MANY_REQUESTS, {"quota_used": used, "quota_limit": limit})


class SecondaryPasswordLockedError(APIException):
    """Secondary password lockout errors."""
    
    def __init__(self, lockout_message: str):
        super().__init__(lockout_message, HTTPStatus.LOCKED)


class PaymentError(APIException):
    """Payment processing errors."""
    
    def __init__(self, message: str = "Payment processing failed"):
        super().__init__(message, HTTPStatus.PAYMENT_REQUIRED)


class WebhookDeliveryError(APIException):
    """Webhook delivery errors."""
    
    def __init__(self, message: str = "Webhook delivery failed"):
        super().__init__(message, HTTPStatus.BAD_GATEWAY)