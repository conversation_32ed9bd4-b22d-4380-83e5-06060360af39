"""Helper functions and decorators for admin routes."""
from functools import wraps
from http import HTTPStatus
from datetime import datetime

from flask import jsonify, request, g
from flask_jwt_extended import get_jwt_identity

from ..models import <PERSON><PERSON>, <PERSON><PERSON>ey, AuditLog, Team, TeamMember, Role
from ..extensions import db


def audit_log(action: str):
    """Decorator to log an admin action."""

    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            response = fn(*args, **kwargs)
            user_id = get_jwt_identity()
            log = AuditLog(
                user_id=user_id,
                action=action,
                details={
                    "ip_address": request.remote_addr,
                    "user_agent": request.user_agent.string,
                    "endpoint": request.path,
                    "kwargs": kwargs,
                },
            )
            db.session.add(log)
            db.session.commit()
            return response

        return wrapper

    return decorator


def admin_required(fn):
    """Decorator to protect admin-only endpoints."""

    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        current_user: User | None = User.query.get(user_id)
        if not current_user or not current_user.has_role("Super Admin"):
            return (
                jsonify({"msg": "Admins only!"}),
                HTTPStatus.FORBIDDEN,
            )
        return fn(*args, **kwargs)

    return wrapper


def sales_required(fn):
    """Decorator to protect sales-only endpoints."""

    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        current_user: User | None = User.query.get(user_id)
        if not current_user or not (
            current_user.has_role("Sales") or current_user.has_role("Super Admin")
        ):
            return (
                jsonify({"msg": "Sales or admins only!"}),
                HTTPStatus.FORBIDDEN,
            )
        return fn(*args, **kwargs)

    return wrapper


def marketing_required(fn):
    """Decorator to protect marketing-only endpoints."""

    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        current_user: User | None = User.query.get(user_id)
        if not current_user or not (
            current_user.has_role("Marketing") or current_user.has_role("Super Admin")
        ):
            return (
                jsonify({"msg": "Marketing or admins only!"}),
                HTTPStatus.FORBIDDEN,
            )
        return fn(*args, **kwargs)

    return wrapper


def developer_required(fn):
    """Decorator to protect developer-only endpoints."""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        current_user: User | None = User.query.get(user_id)
        if not current_user or not (
            current_user.has_role("Developer Admin") or current_user.has_role("Super Admin")
        ):
            return (
                jsonify({"msg": "Developer Admin or Super Admin only!"}),
                HTTPStatus.FORBIDDEN,
            )
        return fn(*args, **kwargs)
    return wrapper


def support_required(fn):
    """Decorator to protect support-only endpoints."""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        current_user: User | None = User.query.get(user_id)
        if not current_user or not (
            current_user.has_role("Support") or current_user.has_role("Super Admin")
        ):
            return (
                jsonify({"msg": "Support or Super Admin only!"}),
                HTTPStatus.FORBIDDEN,
            )
        return fn(*args, **kwargs)
    return wrapper


def team_admin_required(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        team_id = kwargs.get('team_id') or request.view_args.get('team_id')
        team = Team.query.get(team_id)
        if not team:
            return jsonify({"msg": "Team not found"}), HTTPStatus.NOT_FOUND
        if team.owner_id == user_id:
            return fn(*args, **kwargs)
        member = TeamMember.query.filter_by(team_id=team.id, user_id=user_id).first()
        if member and member.role == "admin":
            return fn(*args, **kwargs)
        return jsonify({"msg": "Only team admins/owners can perform this action"}), HTTPStatus.FORBIDDEN
    return wrapper


def team_member_required(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        user_id = get_jwt_identity()
        team_id = kwargs.get('team_id') or request.view_args.get('team_id')
        team = Team.query.get(team_id)
        if not team:
            return jsonify({"msg": "Team not found"}), HTTPStatus.NOT_FOUND
        if team.owner_id == user_id:
            return fn(*args, **kwargs)
        member = TeamMember.query.filter_by(team_id=team.id, user_id=user_id).first()
        if member:
            return fn(*args, **kwargs)
        return jsonify({"msg": "Only team members can perform this action"}), HTTPStatus.FORBIDDEN
    return wrapper
