"""Request ID middleware for tracing requests across the system."""
from __future__ import annotations

import uuid
from flask import request, g


class RequestIDMiddleware:
    """Middleware to add unique request IDs for tracing."""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the middleware with the Flask app."""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """Generate and store request ID before processing request."""
        # Check if request ID is provided in headers (for distributed tracing)
        request_id = request.headers.get('X-Request-ID')
        
        if not request_id:
            # Generate new request ID
            request_id = str(uuid.uuid4())
        
        # Store in Flask's g object for access throughout request
        g.request_id = request_id
    
    def after_request(self, response):
        """Add request ID to response headers."""
        if hasattr(g, 'request_id'):
            response.headers['X-Request-ID'] = g.request_id
        return response
