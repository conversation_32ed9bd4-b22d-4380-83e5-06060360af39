"""Endpoints for managing support tickets."""
from __future__ import annotations

from http import HTTPStatus

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity, jwt_required

from ..extensions import db
from ..models import Ticket, TicketMessage, User
from ..schemas import TicketSchema, TicketMessageSchema
from ..routes.admin_helpers import support_required
from ..routes.webhooks import trigger_support_ticket_created_webhook

support_bp = Blueprint("support", __name__)

ticket_schema = TicketSchema()
tickets_schema = TicketSchema(many=True)
ticket_message_schema = TicketMessageSchema()


@support_bp.route("/tickets", methods=["POST"])
@jwt_required()
def create_ticket():
    """Create a new support ticket."""
    user_id = get_jwt_identity()
    current_user = User.query.get_or_404(user_id)
    data = request.get_json() or {}
    ticket = Ticket(subject=data.get("subject"), user=current_user)
    db.session.add(ticket)
    db.session.commit()
    trigger_support_ticket_created_webhook(
        ticket_id=ticket.id,
        user_id=ticket.user_id,
        subject=ticket.subject,
    )
    return ticket_schema.jsonify(ticket), HTTPStatus.CREATED


@support_bp.route("/tickets", methods=["GET"])
@jwt_required()
def list_tickets():
    """List all support tickets for the current user."""
    user_id = get_jwt_identity()
    tickets = Ticket.query.filter_by(user_id=user_id).all()
    return tickets_schema.jsonify(tickets)


@support_bp.route("/tickets/all", methods=["GET"])
@jwt_required()
@support_required
def list_all_tickets():
    """List all support tickets (support/admin only)."""
    tickets = Ticket.query.all()
    return tickets_schema.jsonify(tickets)


@support_bp.route("/tickets/<int:ticket_id>", methods=["GET"])
@jwt_required()
def get_ticket(ticket_id: int):
    """Get details for a specific support ticket."""
    user_id = get_jwt_identity()
    current_user = User.query.get_or_404(user_id)
    ticket = Ticket.query.get_or_404(ticket_id)
    # Only the ticket owner or support/admin can view
    if ticket.user_id != current_user.id and not current_user.has_role("Support") and not current_user.has_role("Super Admin"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    return ticket_schema.jsonify(ticket)


@support_bp.route("/tickets/<int:ticket_id>/messages", methods=["POST"])
@jwt_required()
def reply_to_ticket(ticket_id: int):
    """Add a message to a support ticket."""
    user_id = get_jwt_identity()
    current_user = User.query.get_or_404(user_id)
    ticket = Ticket.query.get_or_404(ticket_id)
    # Only the ticket owner or support/admin can reply
    if ticket.user_id != current_user.id and not current_user.has_role("Support") and not current_user.has_role("Super Admin"):
        return jsonify({"msg": "Access denied"}), HTTPStatus.FORBIDDEN
    data = request.get_json() or {}
    message = TicketMessage(
        ticket=ticket, user=current_user, message=data.get("message")
    )
    db.session.add(message)
    db.session.commit()
    return ticket_message_schema.jsonify(message), HTTPStatus.CREATED
